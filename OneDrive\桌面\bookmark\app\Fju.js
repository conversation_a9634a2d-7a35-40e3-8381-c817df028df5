class FJU {
    constructor() {
        this.curlevel = 1;
        this.fjutype = null;
    }

    AppendElement(curlevel = 1, type = null) {
        Dom.TopContent.innerHTML = "";
        this.curlevel = curlevel;
        this.fjutype = type;

        const renderMap = {
            1: () => this.level1Element(),
            2: {
                login: () => this.level2loginElement(),
                fee: () => this.level2feeElement()
            }
        };

        if (typeof renderMap[curlevel] === "function") {
            renderMap[curlevel]();
        } else if (renderMap[curlevel] && typeof renderMap[curlevel][type] === "function") {
            renderMap[curlevel][type]();
        }
    }

    // Refresh the FJU page content (useful when language changes)
    refresh() {
        // Clear all content and recreate with current level and type
        Dom.TopContent.innerHTML = "";
        this.AppendElement(this.curlevel, this.fjutype);
    }

    createItem({ id, iconId, text, onClick }) {
        const item = document.createElement('div');
        item.className = "fjuitem";
        if (id) item.id = id;

        const icon = document.createElement('div');
        icon.className = "fjuicon";
        if (iconId) icon.id = iconId;

        const textDiv = document.createElement('div');
        textDiv.className = "fjutext";
        textDiv.textContent = text || "";

        item.append(icon, textDiv);
        if (onClick) item.addEventListener('click', onClick);

        return item;
    }

    createBackButton() {
        const backbtn = document.createElement('div');
        backbtn.id = "backbtn";
        backbtn.addEventListener('click', () => {
            this.curlevel = Math.max(1, this.curlevel - 1);
            this.fjutype = null;
            this.AppendElement(this.curlevel);
        });
        return backbtn;
    }

    level1Element() {
        const container = document.createElement('div');
        container.id = "fjucontainer";

        container.append(
            this.createItem({
                id: "fjuloginarea",
                iconId: "fjuloginicon",
                text: "登入",
                onClick: () => this.AppendElement(2, "login")
            }),
            this.createItem({
                id: "learnfeearea",
                iconId: "fjufeeicon",
                text: "學費",
                onClick: () => this.AppendElement(2, "fee")
            })
        );

        Dom.TopContent.appendChild(container);
    }

    level2loginElement() {
        Dom.TopContent.appendChild(this.createBackButton());

        const container = document.createElement('div');
        container.id = "fjucontainer";

        container.append(
            this.createItem({
                id: "fjustudentdata",
                iconId: "fjudataicon",
                text: "個資管理系統",
                onClick: () => window.location.href = "http://smis.fju.edu.tw/freshman/login.aspx"
            }),
            this.createItem({
                id: "fjustudentmanage",
                iconId: "fjumanageicon",
                text: "一般登入",
                onClick: () => window.location.href = "https://portal.fju.edu.tw/student/Account/Login"
            })
        );

        Dom.TopContent.appendChild(container);
    }

    level2feeElement() {
        Dom.TopContent.appendChild(this.createBackButton());

        const container = document.createElement('div');
        container.id = "fjucontainer";

        container.append(
            this.createItem({
                id: "fjustudentdata",
                iconId: "tsbankicon",
                text: this.getText('fju.feeOptions.paymentPortal'),
                onClick: () => window.location.href = "https://school.taishinbank.com.tw/PORTAL/Auth/Login.aspx"
            }),
            this.createItem({
                id: "fjustudentmanage",
                iconId: "twbankicon",
                text: this.getText('fju.feeOptions.loanPortal'),
                onClick: () => window.location.href = "https://sloan.bot.com.tw/customer/login/SLoanLogin.action"
            })
        );

        Dom.TopContent.appendChild(container);
    }

    // Get text based on current language
    getText(key) {
        if (window.Lang) {
            return window.Lang.t(key);
        }
        // Fallback values
        const fallbacks = {
            'fju.login': '登入',
            'fju.fee': '學費',
            'fju.loginOptions.dataManagement': '個資管理系統',
            'fju.loginOptions.generalLogin': '一般登入',
            'fju.feeOptions.paymentPortal': '繳費入口',
            'fju.feeOptions.loanPortal': '學貸入口'
        };
        return fallbacks[key] || key;
    }
}
