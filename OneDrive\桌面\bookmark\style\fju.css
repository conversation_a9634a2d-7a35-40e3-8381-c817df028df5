#fjucontainer {
    width: 80%;
    height: 100%;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
}

.fjuitem {
    display: flex;
    align-items: center;
    width: 30%;
    height: 80%;
    padding: 0 10px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(12px) saturate(180%);
    -webkit-backdrop-filter: blur(12px) saturate(180%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    transition: all 0.45s;
    justify-content: space-evenly;
    flex-direction: column;
    user-select: none;
}

.fjuitem:hover {
    transform: scale(1.05);
}

.fjuicon {
    width: 75%;
    height: 50%;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
}

.fjutext {
    font-size: 35px;
    font-weight: 600;
}

#fjuloginicon {
    background-image: url(../utils/fju/login.png);
}

#fjufeeicon {
    background-image: url(../utils/fju/fee.png);
}

#fjudataicon {
    background-image: url(../utils/fju/data.png);
}

#fjumanageicon{
    background-image: url(../utils/fju.png);
}

#tsbankicon{
    background-image: url(https://zh.wikipedia-on-ipfs.org/I/Taishin.svg.png.webp);
}

#twbankicon{
    background-image: url(https://upload.wikimedia.org/wikipedia/commons/thumb/a/a9/Bank_of_Taiwan_Seal.svg/800px-Bank_of_Taiwan_Seal.svg.png);
}

/* Responsive Design for FJU Page */
@media (max-width: 1024px) {
    #fjucontainer {
        width: 90%;
    }

    .fjuitem {
        width: 32%;
        height: 75%;
    }

    .fjutext {
        font-size: 30px;
    }
}

@media (max-width: 768px) {
    #fjucontainer {
        width: 95%;
        flex-direction: column;
        gap: 20px;
    }

    .fjuitem {
        width: 80%;
        height: 35%;
        border-radius: 15px;
    }

    .fjutext {
        font-size: 28px;
    }

    .fjuicon {
        width: 60%;
        height: 40%;
    }
}

@media (max-width: 640px) {
    .fjuitem {
        width: 85%;
        height: 32%;
    }

    .fjutext {
        font-size: 25px;
    }
}

@media (max-width: 480px) {
    #fjucontainer {
        width: 98%;
        gap: 15px;
    }

    .fjuitem {
        width: 90%;
        height: 30%;
        border-radius: 12px;
        padding: 0 8px;
    }

    .fjutext {
        font-size: 22px;
    }

    .fjuicon {
        width: 50%;
        height: 35%;
    }
}

@media (max-width: 360px) {
    .fjuitem {
        width: 95%;
        height: 28%;
        border-radius: 10px;
    }

    .fjutext {
        font-size: 20px;
    }

    .fjuicon {
        width: 45%;
        height: 32%;
    }
}

@media (max-width: 320px) {
    #fjucontainer {
        gap: 10px;
    }

    .fjuitem {
        height: 26%;
    }

    .fjutext {
        font-size: 18px;
    }

    .fjuicon {
        width: 40%;
        height: 30%;
    }
}