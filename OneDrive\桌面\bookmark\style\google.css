#googlecontainer {
    width: 80%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

#googlecard {
    display: flex;
    align-items: center;
    width: 45%;
    height: 95%;
    padding: 0 10px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(12px) saturate(180%);
    -webkit-backdrop-filter: blur(12px) saturate(180%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    transition: all 0.45s;
    justify-content: space-evenly;
    flex-direction: column;
}

#googletit {
    width: 90%;
    height: 12%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28px;
    letter-spacing: 2px;
    font-weight: 600;
    color: #444;
    border-bottom: 2px solid #ccc;
}

#appcontainer {
    width: 95%;
    height: 85%;
    padding-left: 40px;
    padding-top: 15px;
    display: flex;
    flex-wrap: wrap;
    justify-content: start;
    align-items: start;
    overflow: auto;
    row-gap: 1.5%;
    column-gap: 3.5%;

}


.appitem {
    width: 28%;
    height: 160px;
    aspect-ratio: 1;
    min-width: 120px;
    min-height: 120px;
    border-radius: 5px;
    /* background: #eee; */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;
    transition: all 0.3s;
    user-select: none;

}

.appitem:hover {
    filter: drop-shadow(0 0 5px white);
    transform: scale(1.05);
}

.appicon {
    width: 85%;
    height: 50%;
    background-position: center;
    background-repeat: no-repeat;
}

.appname {
    font-weight: 600;
    font-size: 20px;
    color: #444;
    display: flex;
    align-items: center;
    justify-content: center;
}

#gmailicon {
    background-image: url(https://cdn1.iconfinder.com/data/icons/google-s-logo/150/Google_Icons-02-1024.png);
    background-size: 120px 120px;
}

#yticon {
    background-image: url(https://cdn2.iconfinder.com/data/icons/social-media-2285/512/1_Youtube_colored_svg-512.png);
    background-size: 80px 80px;
}

#mapicon {
    background-image: url(https://cdn1.iconfinder.com/data/icons/google-s-logo/150/Google_Icons-01-128.png);
    background-size: 100px 100px;
}

#driveicon {
    background-image: url(https://cdn4.iconfinder.com/data/icons/logos-brands-in-colors/48/google-drive-512.png);
    background-size: 90px 90px;
}

#meeticon {
    background-image: url(https://cdn1.iconfinder.com/data/icons/google-new-logos-1/32/google_meet_new_logo-512.png);
    background-size: 90px 90px;
}

#dateicon {
    background-image: url(https://cdn1.iconfinder.com/data/icons/google-s-logo/150/Google_Icons-03-512.png);
    background-size: 110px 110px;
}

#chromeadditionicon {
    background-image: url(https://cdn0.iconfinder.com/data/icons/applications-windows-2/24/Chrome_Webstore_application-512.png);
    background-size: 80px 80px;
}

#geminiicon {
    background-image: url(https://www.pngall.com/wp-content/uploads/16/Google-Gemini-Logo-PNG-Photo.png);
    background-size: 90px 90px;
}

#googleplayicon {
    background-image: url(https://cdn4.iconfinder.com/data/icons/social-media-logos-6/512/103-GooglePlay_play_google_play_apps-128.png);
    background-size: 70px 70px;
}

#googleformsicon {
    background-image: url(https://cdn4.iconfinder.com/data/icons/bloomies-files-and-documents/25/Google_Forms_Social_Interface-512.png);
    background-size: 110px 110px;
}

#googletranslationicon {
    background-image: url(https://cdn2.iconfinder.com/data/icons/social-media-2259/512/translate-512.png);
    background-size: 90px 90px;
}

/* Responsive Design for Google Page */
@media screen and (max-width: 1836px) {
    #appcontainer {
        row-gap: 2%;
        column-gap: 5%;
    }
    .appitem {
        width: 28%;
    }
}

@media screen and (max-width: 1024px) {
    #googlecontainer {
        width: 90%;
    }

    #googlecard {
        width: 60%;
    }

    #googletit {
        font-size: 24px;
    }

    #appcontainer {
        padding-left: 30px;
        column-gap: 4%;
    }

    .appitem {
        width: 30%;
        height: 140px;
        min-width: 100px;
        min-height: 100px;
    }

    .appname {
        font-size: 18px;
    }
}

@media screen and (max-width: 768px) {
    #googlecontainer {
        width: 95%;
    }

    #googlecard {
        width: 80%;
        border-radius: 15px;
    }

    #googletit {
        font-size: 22px;
        height: 10%;
    }

    #appcontainer {
        padding-left: 20px;
        padding-top: 10px;
        column-gap: 3%;
        row-gap: 3%;
    }

    .appitem {
        width: 45%;
        height: 120px;
        min-width: 90px;
        min-height: 90px;
    }

    .appname {
        font-size: 16px;
    }
}

@media screen and (max-width: 640px) {
    #googlecard {
        width: 90%;
    }

    #appcontainer {
        padding-left: 15px;
        column-gap: 2%;
        row-gap: 4%;
    }

    .appitem {
        width: 47%;
        height: 110px;
    }

    .appname {
        font-size: 15px;
    }
}

@media screen and (max-width: 480px) {
    #googlecontainer {
        width: 98%;
    }

    #googlecard {
        width: 95%;
        border-radius: 12px;
        padding: 0 5px;
    }

    #googletit {
        font-size: 20px;
        letter-spacing: 1px;
    }

    #appcontainer {
        padding-left: 10px;
        padding-top: 8px;
        column-gap: 2%;
        row-gap: 5%;
    }

    .appitem {
        width: 48%;
        height: 100px;
        min-width: 80px;
        min-height: 80px;
    }

    .appname {
        font-size: 14px;
    }
}

@media screen and (max-width: 360px) {
    #googlecard {
        width: 98%;
        border-radius: 10px;
    }

    #googletit {
        font-size: 18px;
    }

    #appcontainer {
        padding-left: 8px;
        padding-top: 5px;
    }

    .appitem {
        width: 48%;
        height: 90px;
        min-width: 70px;
        min-height: 70px;
    }

    .appname {
        font-size: 13px;
    }
}

@media screen and (max-width: 320px) {
    #googletit {
        font-size: 16px;
        letter-spacing: 0.5px;
    }

    #appcontainer {
        padding-left: 5px;
    }

    .appitem {
        height: 85px;
        min-width: 65px;
        min-height: 65px;
    }

    .appname {
        font-size: 12px;
    }
}