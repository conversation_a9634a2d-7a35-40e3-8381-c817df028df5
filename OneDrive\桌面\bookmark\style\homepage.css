.logo {
    font-size: 80px;
    color: #333;
    text-shadow: 0 0 5px #888;
    user-select: none;
}


.apple-search {
    display: flex;
    align-items: center;
    width: 80%;
    height: 60px;
    padding: 0 10px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(12px) saturate(180%);
    -webkit-backdrop-filter: blur(12px) saturate(180%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.apple-search input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 25px;
    color: #333;
}

.apple-search input::placeholder {
    color: #444;
}

.apple-search button {
    border: none;
    background: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 30px;
    transform-origin: center;
    transition: all .6s;
}

.apple-search button:hover {
    font-size: 35px;
}

/* Responsive Design for Homepage */
@media (max-width: 1024px) {
    .logo {
        font-size: 70px;
    }

    .apple-search {
        width: 85%;
        height: 55px;
    }

    .apple-search input {
        font-size: 22px;
    }

    .apple-search button {
        width: 55px;
        height: 55px;
        font-size: 28px;
    }

    .apple-search button:hover {
        font-size: 32px;
    }
}

@media (max-width: 768px) {
    .logo {
        font-size: 60px;
    }

    .apple-search {
        width: 90%;
        height: 50px;
        padding: 0 8px;
        border-radius: 15px;
    }

    .apple-search input {
        font-size: 20px;
    }

    .apple-search button {
        width: 50px;
        height: 50px;
        font-size: 25px;
    }

    .apple-search button:hover {
        font-size: 28px;
    }
}

@media (max-width: 480px) {
    .logo {
        font-size: 50px;
    }

    .apple-search {
        width: 95%;
        height: 45px;
        padding: 0 6px;
        border-radius: 12px;
    }

    .apple-search input {
        font-size: 18px;
    }

    .apple-search button {
        width: 45px;
        height: 45px;
        font-size: 22px;
    }

    .apple-search button:hover {
        font-size: 25px;
    }
}

@media (max-width: 360px) {
    .logo {
        font-size: 45px;
    }

    .apple-search {
        width: 98%;
        height: 40px;
        padding: 0 5px;
        border-radius: 10px;
    }

    .apple-search input {
        font-size: 16px;
    }

    .apple-search button {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }

    .apple-search button:hover {
        font-size: 22px;
    }
}

@media (max-width: 320px) {
    .logo {
        font-size: 40px;
    }

    .apple-search {
        height: 38px;
    }

    .apple-search input {
        font-size: 15px;
    }

    .apple-search button {
        width: 38px;
        height: 38px;
        font-size: 18px;
    }

    .apple-search button:hover {
        font-size: 20px;
    }
}