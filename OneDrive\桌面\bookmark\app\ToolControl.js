class ToolBar {
    constructor() {
        this.defaultPage = "Home";
        this.curPage = this.defaultPage;
    }

    async ChangePage(curPage) {
        Dom.TopContent.innerHTML = "";

        const page = Pages[`${curPage}`];
        if (page && typeof page.AppendElement === 'function') {
            await page.AppendElement();
        }
    }

    PageBind() {
        Dom.ToolBarContainer.addEventListener('click', async (e) => {
            if (e.target.id === "homebtn") {
                this.curPage = "Home";
            } else if (e.target.id === "fjubtn") {
                this.curPage = "FJU";
            } else if (e.target.id === "googlebtn") {
                this.curPage = "Google";
            } else if (e.target.id === "learnbtn") {
                this.curPage = "Learn";
            } else if (e.target.id === "gamebtn") {
                this.curPage = "Game";
            } else if (e.target.id === "videobtn") {
                this.curPage = "Video";
            } else if (e.target.id === "jobbtn") {
                this.curPage = "Job";
            } else if (e.target.id === "toolbtn") {
                this.curPage = "Tool";
            } else if (e.target.id === "settingbtn") {
                this.curPage = "Setting";
            }
            await this.ChangePage(this.curPage);
        })
    }
}