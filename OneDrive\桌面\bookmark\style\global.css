* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

*::-webkit-scrollbar-button {
    display: none;
}

*::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

*::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 4px;
}

#container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-color: #ddd;
}

#container::after {
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 0;
    background-color: #e5e5f7;
    opacity: 0.1;
    background: radial-gradient(circle, transparent 20%, #e5e5f7 20%, #e5e5f7 80%, transparent 80%, transparent), radial-gradient(circle, transparent 20%, #e5e5f7 20%, #e5e5f7 80%, transparent 80%, transparent) 100px 100px, linear-gradient(#444cf7 8px, transparent 8px) 0 -4px, linear-gradient(90deg, #444cf7 8px, #e5e5f7 8px) -4px 0;
    background-size: 200px 200px, 200px 200px, 100px 100px, 100px 100px;
}

#top-area {
    width: 100%;
    height: 85%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

#bottom-area {
    width: 100%;
    height: 15%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: visible;
    z-index: 1;
}

#backbtn {
    position: fixed;
    top: 20px;
    left: 20px;
    width: 80px;
    height: 80px;
    background-image: url(../utils/backbtn.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    cursor: pointer;
    transition: all 0.3s;
}

#backbtn:hover {
    transform: scale(1.05);
}

#settingbtn {
    width: 50px;
    height: 50px;
    position: fixed;
    top: 20px;
    right: 20px;
    background-image: url(../utils/setting.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    filter: drop-shadow(0 0 2px black);
    cursor: pointer;
    z-index: 999;
    transition: all 0.3s;
}

#settingbtn:hover {
    transform: scale(1.05);
}

/* Responsive Design */
@media (max-width: 768px) {
    #container {
        padding: 10px;
    }

    #top-area {
        height: 80%;
    }

    #bottom-area {
        height: 20%;
    }

    #backbtn {
        top: 10px;
        left: 10px;
        width: 60px;
        height: 60px;
    }

    #settingbtn {
        top: 10px;
        right: 10px;
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 480px) {
    #container {
        padding: 5px;
    }

    #top-area {
        height: 75%;
    }

    #bottom-area {
        height: 25%;
    }

    #backbtn {
        top: 5px;
        left: 5px;
        width: 50px;
        height: 50px;
    }

    #settingbtn {
        top: 5px;
        right: 5px;
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 320px) {
    #container {
        padding: 2px;
    }

    #backbtn {
        width: 45px;
        height: 45px;
    }

    #settingbtn {
        width: 30px;
        height: 30px;
    }
}