// 日本語言語パック
const ja = {
    // 共通
    common: {
        home: 'ホーム',
        learn: '学習',
        game: 'ゲーム',
        video: '動画',
        job: '仕事',
        settings: '設定',
        search: '検索',
        add: '追加',
        edit: '編集',
        delete: '削除',
        save: '保存',
        cancel: 'キャンセル',
        confirm: '確認',
        back: '戻る',
        loading: '読み込み中...',
        error: 'エラー',
        success: '成功',
        warning: '警告',
        info: '情報'
    },

    // ホーム
    home: {
        welcome: 'ブックマークマネージャーへようこそ',
        welcomeMessage: '{0}のブックマーク',
        searchPlaceholder: 'Googleで検索...',
        quickAccess: 'クイックアクセス',
        recentBookmarks: '最近のブックマーク'
    },

    // Google ページ
    google: {
        title: 'Google アプリ',
        apps: {
            gmail: 'Gmail',
            youtube: 'Youtube',
            maps: 'マップ',
            drive: 'ドライブ',
            meet: 'Meet',
            calendar: 'カレンダー',
            chromeStore: 'Chrome ストア',
            gemini: 'Gemini',
            play: 'Play',
            forms: 'Google フォーム',
            translation: '翻訳'
        }
    },

    // FJU ページ
    fju: {
        title: '輔仁大学',
        login: 'ログイン',
        fee: '学費',
        back: '戻る',
        loginOptions: {
            dataManagement: '個人情報管理システム',
            generalLogin: '一般ログイン'
        },
        feeOptions: {
            paymentPortal: '支払いポータル',
            loanPortal: '学生ローンポータル'
        }
    },

    // Tool ページ
    tool: {
        title: 'ツールボックス',
        loading: 'ツールを読み込み中...',
        accessTitle: 'ブックマークアクセス',
        accessText: 'ブックマークのツールフォルダを読み取るには、WebExtension対応ブラウザでこのページを拡張機能として読み込む必要があります。',
        fallbackText: '推奨オンラインツール：',
        errorTitle: '読み込みエラー',
        errorText: 'ツールブックマークを読み込めません。ブラウザの権限設定を確認してください。',
        noToolsTitle: 'ツールが見つかりません',
        noToolsText: 'ブックマークに「tool」または「ツール」という名前のフォルダを作成し、ツールウェブサイトを追加してください。',
        tools: {
            colorPicker: 'カラーピッカー',
            colorPickerDesc: '色コードの選択と生成',
            qrGenerator: 'QRコード生成器',
            qrGeneratorDesc: 'カスタムQRコードの生成',
            jsonFormatter: 'JSONフォーマッター',
            jsonFormatterDesc: 'JSONデータのフォーマットと検証',
            base64Encoder: 'Base64エンコーダー',
            base64EncoderDesc: 'Base64文字列のエンコードとデコード',
            urlShortener: 'URL短縮',
            urlShortenerDesc: '長いURLの短縮',
            passwordGenerator: 'パスワード生成器',
            passwordGeneratorDesc: '安全なパスワードの生成',
            imageCompressor: '画像圧縮',
            imageCompressorDesc: '画像ファイルサイズの圧縮',
            regexTester: '正規表現テスター',
            regexTesterDesc: '正規表現のテストとデバッグ',
            markdownEditor: 'Markdownエディター',
            markdownEditorDesc: 'オンラインMarkdownエディターとプレビュー',
            calculator: '計算機',
            calculatorDesc: 'オンライン科学計算機',
            unitConverter: '単位変換',
            unitConverterDesc: '各種単位変換ツール',
            codeBeautifier: 'コード整形',
            codeBeautifierDesc: 'コードのフォーマットと整形'
        }
    },

    // ブックマーク
    bookmarks: {
        title: 'ブラウザブックマーク',
        searchPlaceholder: 'ブックマークを検索...',
        addBookmark: 'ブックマーク追加',
        addFolder: 'フォルダ追加',
        addCurrentTab: '現在のタブを追加',
        addCustomBookmark: 'カスタムブックマーク追加',
        editBookmark: 'ブックマーク編集',
        deleteBookmark: 'ブックマーク削除',
        renameFolder: '名前変更',
        moveItem: '移動',
        openBookmark: '開く',
        bookmarkTitle: 'ブックマークタイトル',
        bookmarkUrl: 'ブックマークURL',
        customIconUrl: 'カスタムアイコンURL',
        folderName: 'フォルダ名',
        enterUrl: 'URLを入力',
        enterTitle: 'タイトルを入力（オプション）',
        enterFolderName: 'フォルダ名を入力',
        enterNewName: '新しい名前を入力',
        enterTargetFolder: 'ターゲットフォルダ名を入力（空白でルート）',
        iconPreview: 'アイコンプレビューがここに表示されます',
        iconLoadError: 'アイコンを読み込めません',
        dropHere: 'ここにドロップ',
        backToParent: '親フォルダに戻る',
        untitled: '無題',
        unnamedFolder: '（名前なしフォルダ）',
        categories: {
            folder: 'フォルダ',
            video: '動画',
            development: '開発',
            google: 'Google',
            reading: '読書',
            school: '学校',
            other: 'その他'
        }
    },

    // ブックマークページ
    bookmarks: {
        title: 'ブックマーク',
        search: 'ブックマークを検索...',
        addBookmark: 'ブックマーク追加',
        addFolder: 'フォルダ追加',
        editBookmark: 'ブックマーク編集',
        renameFolder: '名前変更',
        moveItem: '移動',
        deleteItem: '削除',
        cancel: 'キャンセル',
        confirm: '確認',
        save: '保存',
        open: '開く',

        // フォームラベル
        form: {
            title: 'タイトル',
            url: 'URL',
            icon: 'カスタムアイコンURL',
            folderName: 'フォルダ名',
            titlePlaceholder: 'ブックマークタイトルを入力',
            urlPlaceholder: 'https://example.com',
            iconPlaceholder: 'https://example.com/icon.png (オプション)',
            folderPlaceholder: 'フォルダ名を入力'
        },

        // メッセージ
        messages: {
            unnamedFolder: '(名前なしフォルダ)',
            deleteBookmarkTitle: 'ブックマーク削除',
            deleteFolderTitle: 'フォルダ削除'
        },

        // エラーメッセージ
        errors: {
            addError: 'ブックマーク追加時にエラーが発生しました',
            editError: 'ブックマーク更新時にエラーが発生しました'
        },

        // 移動機能
        move: {
            moveItemDesc: '"{item}" を移動するフォルダを選択してください',
            searchFolders: 'フォルダを検索...',
            moveHere: 'ここに移動',
            moveError: '移動に失敗しました。もう一度お試しください'
        }
    },

    // ウェルカムページ
    welcome: {
        title: 'ブックマークマネージャーへようこそ',
        subtitle: 'パーソナライズされたブックマーク体験を設定しましょう',
        nameLabel: 'お名前',
        namePlaceholder: 'お名前を入力してください',
        startButton: '開始する',
        settingUp: 'ブックマークフォルダを設定中...',
        creatingFolder: '{folder} フォルダを作成中...',
        setupComplete: '設定完了！',
        setupError: '設定エラーが発生しましたが、アプリは正常に使用できます',
        noBookmarkAccess: 'ブックマークにアクセスできません。手動でフォルダを作成してください',
        nameRequired: 'お名前を入力してください',
        nameTooLong: '名前は20文字以内で入力してください'
    },

    // 設定
    settings: {
        title: '設定',
        subtitle: 'ブックマークアプリの体験をカスタマイズ',
        
        // 一般
        general: {
            title: '一般設定',
            username: 'ユーザー名',
            usernameDesc: 'アプリに表示される名前',
            defaultPage: 'デフォルトページ',
            defaultPageDesc: 'アプリ起動時に表示されるページ',
            language: '言語',
            languageDesc: 'アプリインターフェース言語'
        },





        // データ
        data: {
            title: 'データ管理',
            exportSettings: '設定をエクスポート',
            exportSettingsDesc: 'すべての設定をファイルにエクスポート',
            importSettings: '設定をインポート',
            importSettingsDesc: 'ファイルから設定をインポート',
            resetSettings: '設定をリセット',
            resetSettingsDesc: 'すべての設定をデフォルトに復元',
            clearAllData: 'すべてのデータをクリア',
            clearAllDataDesc: 'すべてのカスタムアイコン、順序、設定をクリア'
        },

        // オプション
        options: {
            languages: {
                zh: '繁體中文',
                en: 'English',
                ja: '日本語'
            },
            pages: {
                Home: 'ホーム',
                Learn: '学習',
                Game: 'ゲーム',
                Video: '動画',
                Job: '仕事'
            }
        }
    },

    // メッセージ
    messages: {
        settingsExported: '設定がエクスポートされました',
        settingsImported: '設定がインポートされました',
        settingsReset: '設定がリセットされました',
        dataCleared: 'すべてのデータがクリアされました',
        importFailed: 'インポート失敗：無効なファイル形式',
        confirmDelete: '"{0}"を削除してもよろしいですか？',
        confirmReset: 'すべての設定をリセットしてもよろしいですか？この操作は元に戻せません。',
        confirmClearData: 'すべてのデータをクリアしてもよろしいですか？これによりすべてのカスタムアイコン、順序、設定が削除されます。この操作は元に戻せません。',
        folderNotFound: '指定されたフォルダが見つかりません',
        targetFolderMustBeInScope: 'ターゲットフォルダは{0}スコープ内である必要があります',
        cannotGetCurrentTab: '現在のタブを取得できません（タブ権限がないかブラウザがサポートしていません）。',
        bookmarksApiRequired: 'ブックマークを同期するには、WebExtension対応ブラウザでこのページを拡張機能として読み込む必要があります（ブックマーク権限が必要）。',
        bookmarksLoadError: 'ブックマーク読み込みエラー：{0}'
    }
};

// 言語パックをエクスポート
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ja;
} else {
    window.ja = ja;
}
