/* Settings Page Styles */
#settings-container {
  width: 100%;
  height: 100%;
  background: #f5f5f7;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-y: auto;
  padding: 40px;
  box-sizing: border-box;
}

#settings-header {
  text-align: center;
  margin-bottom: 40px;
}

#settings-title {
  font-size: 36px;
  font-weight: 700;
  color: #1D1D1F;
  margin: 0 0 8px 0;
}

#settings-subtitle {
  font-size: 18px;
  color: #666;
  margin: 0;
  font-weight: 400;
}

#settings-content {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Settings Sections */
.settings-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #1D1D1F;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
}

/* Setting Items */
.setting-item {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.setting-label {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 4px;
}

.setting-description {
  font-size: 14px;
  color: #666;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

/* Input Controls */
.setting-input,
.setting-select {
  width: 100%;
  max-width: 300px;
  height: 44px;
  padding: 0 16px;
  border: 1px solid #E5E5EA;
  border-radius: 8px;
  font-size: 16px;
  background: white;
  transition: border-color 0.2s ease;
}

.setting-input:focus,
.setting-select:focus {
  outline: none;
  border-color: #007AFF;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

/* Toggle Switch */
.toggle-container {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.setting-toggle {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 34px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.setting-toggle:checked + .toggle-slider {
  background-color: #007AFF;
}

.setting-toggle:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Range Slider */
.range-container {
  display: flex;
  align-items: center;
  gap: 16px;
  max-width: 400px;
}

.setting-range {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #E5E5EA;
  outline: none;
  -webkit-appearance: none;
}

.setting-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #007AFF;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.setting-range::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #007AFF;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-value {
  font-size: 14px;
  font-weight: 600;
  color: #007AFF;
  min-width: 60px;
  text-align: center;
  background: #E3F2FD;
  padding: 4px 8px;
  border-radius: 6px;
}

/* Buttons */
.setting-button {
  height: 44px;
  padding: 0 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.setting-button.primary {
  background: #007AFF;
  color: white;
}

.setting-button.primary:hover {
  background: #0056CC;
  transform: translateY(-1px);
}

.setting-button.danger {
  background: #FF3B30;
  color: white;
}

.setting-button.danger:hover {
  background: #D70015;
  transform: translateY(-1px);
}

/* Notifications */
.settings-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 16px 24px;
  border-radius: 8px;
  font-weight: 500;
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

.settings-notification.success {
  background: #34C759;
  color: white;
}

.settings-notification.error {
  background: #FF3B30;
  color: white;
}

.settings-notification.info {
  background: #007AFF;
  color: white;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Theme Support */
.theme-dark #settings-container {
  background: #1C1C1E;
  color: #FFFFFF;
}

.theme-dark .settings-section {
  background: #2C2C2E;
  border-color: #38383A;
}

.theme-dark .section-title,
.theme-dark .setting-label {
  color: #FFFFFF;
}

.theme-dark .setting-description {
  color: #AEAEB2;
}

.theme-dark .setting-input,
.theme-dark .setting-select {
  background: #1C1C1E;
  border-color: #38383A;
  color: #FFFFFF;
}

.theme-dark .setting-item {
  border-bottom-color: #38383A;
}

/* Compact Mode */
.compact-mode .settings-section {
  padding: 16px;
}

.compact-mode .setting-item {
  margin-bottom: 16px;
  padding-bottom: 12px;
}

.compact-mode .section-title {
  font-size: 20px;
  margin-bottom: 16px;
}

/* No Animations */
.no-animations * {
  animation: none !important;
  transition: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  #settings-container {
    padding: 20px 16px;
  }
  
  #settings-title {
    font-size: 28px;
  }
  
  #settings-subtitle {
    font-size: 16px;
  }
  
  .settings-section {
    padding: 20px 16px;
  }
  
  .setting-input,
  .setting-select {
    max-width: none;
  }
  
  .range-container {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .range-value {
    text-align: left;
  }
}

/* Enhanced Responsive Design for Settings */
@media (max-width: 1024px) {
  #settings-container {
    padding: 30px;
  }

  #settings-title {
    font-size: 32px;
  }

  #settings-subtitle {
    font-size: 16px;
  }

  #settings-content {
    max-width: 700px;
    gap: 28px;
  }

  .settings-section {
    padding: 20px;
    border-radius: 14px;
  }

  .section-title {
    font-size: 22px;
  }
}

@media (max-width: 768px) {
  #settings-container {
    padding: 20px;
  }

  #settings-header {
    margin-bottom: 30px;
  }

  #settings-title {
    font-size: 28px;
  }

  #settings-subtitle {
    font-size: 15px;
  }

  #settings-content {
    max-width: 100%;
    gap: 24px;
  }

  .settings-section {
    padding: 18px;
    border-radius: 12px;
  }

  .section-title {
    font-size: 20px;
  }

  .setting-item {
    padding: 16px 0;
  }

  .setting-label {
    font-size: 16px;
  }

  .setting-description {
    font-size: 13px;
  }
}

@media (max-width: 640px) {
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .setting-info {
    width: 100%;
  }

  .setting-control {
    width: 100%;
    justify-content: flex-start;
  }

  .select-wrapper,
  .input-wrapper {
    width: 100%;
  }

  .setting-select,
  .setting-input {
    width: 100%;
  }
}

@media (max-width: 480px) {
  #settings-container {
    padding: 15px;
  }

  #settings-header {
    margin-bottom: 25px;
  }

  #settings-title {
    font-size: 24px;
  }

  #settings-subtitle {
    font-size: 14px;
  }

  #settings-content {
    gap: 20px;
  }

  .settings-section {
    padding: 16px;
    border-radius: 10px;
  }

  .section-title {
    font-size: 18px;
    margin-bottom: 16px;
  }

  .setting-item {
    padding: 14px 0;
  }

  .setting-label {
    font-size: 15px;
  }

  .setting-description {
    font-size: 12px;
  }

  .setting-button {
    padding: 10px 16px;
    font-size: 14px;
  }

  .range-container {
    gap: 8px;
  }

  .range-slider {
    height: 6px;
  }

  .range-value {
    font-size: 13px;
  }
}

@media (max-width: 360px) {
  #settings-container {
    padding: 12px;
  }

  #settings-title {
    font-size: 22px;
  }

  #settings-content {
    gap: 18px;
  }

  .settings-section {
    padding: 14px;
  }

  .section-title {
    font-size: 17px;
    margin-bottom: 14px;
  }

  .setting-item {
    padding: 12px 0;
  }

  .setting-label {
    font-size: 14px;
  }

  .setting-description {
    font-size: 11px;
  }

  .setting-button {
    padding: 8px 12px;
    font-size: 13px;
  }
}

@media (max-width: 320px) {
  #settings-container {
    padding: 10px;
  }

  #settings-title {
    font-size: 20px;
  }

  #settings-subtitle {
    font-size: 13px;
  }

  .settings-section {
    padding: 12px;
  }

  .section-title {
    font-size: 16px;
  }

  .setting-label {
    font-size: 13px;
  }

  .setting-description {
    font-size: 10px;
  }

  .setting-button {
    padding: 6px 10px;
    font-size: 12px;
  }
}
