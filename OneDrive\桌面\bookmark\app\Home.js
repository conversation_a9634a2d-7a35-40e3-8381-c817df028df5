class Home {
    AppendElement() {
        this.CreateElement();
    }

    // Refresh the home page content (useful when settings change)
    refresh() {
        const homepage = document.getElementById('homepage');
        if (homepage) {
            homepage.remove();
        }
        this.CreateElement();
    }

    SearchAction(keyword) {
        if (!keyword.trim()) {
            return;
        }
        window.location.href = `https://www.google.com/search?q=${keyword}&oq=${keyword}&sourceid=chrome&ie=UTF-8`;
    }

    CreateElement() {
        const homecontainer = document.createElement('div');
        homecontainer.id = "homepage";

        const logocontainer = document.createElement('div');
        logocontainer.className = "logo";

        // Get username from settings and create personalized welcome message
        const username = this.getUsername();
        const welcomeMessage = this.getWelcomeMessage(username);
        logocontainer.textContent = welcomeMessage;

        const searchcontainer = document.createElement("div");
        searchcontainer.className = "apple-search";

        const searchinput = document.createElement('input');
        searchinput.id = "mysearch";
        searchinput.type = "text";

        // Set placeholder with language support
        const searchPlaceholder = this.getSearchPlaceholder();
        searchinput.placeholder = searchPlaceholder;

        const searchbtn = document.createElement("button");
        searchbtn.id = "confirmsearch";
        searchbtn.textContent = "🔍";

        searchbtn.addEventListener("click", () => {
            this.SearchAction(searchinput.value);
        });

        searchinput.addEventListener("keypress", (e) => {
            if (e.key === "Enter") {
                this.SearchAction(searchinput.value);
            }
        });

        searchcontainer.appendChild(searchinput);
        searchcontainer.appendChild(searchbtn);

        homecontainer.appendChild(logocontainer);
        homecontainer.appendChild(searchcontainer);

        Dom.TopContent.appendChild(homecontainer);
    }

    // Get username from settings
    getUsername() {
        if (window.AppSettings) {
            return window.AppSettings.getSetting('username') || '我';
        }
        return '我';
    }

    // Get welcome message based on current language and username
    getWelcomeMessage(username) {
        if (window.Lang) {
            const welcomeTemplate = window.Lang.t('home.welcomeMessage');
            return `📚 ${window.Lang.formatString(welcomeTemplate, [username])}`;
        }
        return `📚 ${username}的書籤導航`;
    }

    // Get search placeholder based on current language
    getSearchPlaceholder() {
        if (window.Lang) {
            return ` ${window.Lang.t('home.searchPlaceholder')}`;
        }
        return ' 搜尋Google...';
    }
}