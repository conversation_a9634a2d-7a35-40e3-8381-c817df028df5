class Welcome {
    constructor(onComplete = null) {
        this.isFirstTime = this.checkFirstTime();
        this.requiredFolders = ['Tool', 'Learn', 'Job', 'Game', 'Video'];
        this.setupProgress = {
            current: 0,
            total: this.requiredFolders.length,
            messages: []
        };
        this.onComplete = onComplete;
    }

    checkFirstTime() {
        return !localStorage.getItem('bookmark_app_initialized');
    }

    async show() {
        if (!this.isFirstTime) {
            return false;
        }

        this.createWelcomeScreen();
        return true;
    }

    createWelcomeScreen() {
        const overlay = document.createElement('div');
        overlay.id = 'welcome-overlay';
        
        const container = document.createElement('div');
        container.className = 'welcome-container';
        
        container.innerHTML = `
            <div class="welcome-logo">📚</div>
            <h1 class="welcome-title">${this.getText('welcome.title')}</h1>
            <p class="welcome-subtitle">${this.getText('welcome.subtitle')}</p>
            
            <form class="welcome-form" id="welcome-form">
                <div class="welcome-input-group">
                    <label class="welcome-label" for="username">${this.getText('welcome.nameLabel')}</label>
                    <input 
                        type="text" 
                        id="username" 
                        class="welcome-input" 
                        placeholder="${this.getText('welcome.namePlaceholder')}"
                        maxlength="20"
                        required
                    >
                </div>
                <button type="submit" class="welcome-button" id="welcome-submit">
                    ${this.getText('welcome.startButton')}
                </button>
            </form>
            
            <div class="welcome-progress" id="welcome-progress">
                <div class="progress-text" id="progress-text">${this.getText('welcome.settingUp')}</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
            </div>
        `;
        
        overlay.appendChild(container);
        document.body.appendChild(overlay);
        
        this.attachEventListeners();
    }

    attachEventListeners() {
        const form = document.getElementById('welcome-form');
        const submitBtn = document.getElementById('welcome-submit');
        const usernameInput = document.getElementById('username');
        
        // Auto-focus on username input
        setTimeout(() => {
            usernameInput.focus();
        }, 1500);
        
        // Form submission
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const username = usernameInput.value.trim();
            
            if (username.length < 1) {
                this.showError(this.getText('welcome.nameRequired'));
                return;
            }
            
            if (username.length > 20) {
                this.showError(this.getText('welcome.nameTooLong'));
                return;
            }
            
            // Save username
            if (window.AppSettings) {
                window.AppSettings.setSetting('username', username);
            } else {
                localStorage.setItem('bookmark_username', username);
            }
            
            // Disable form and show progress
            submitBtn.disabled = true;
            form.style.opacity = '0.6';
            this.showProgress();
            
            // Start setup process
            await this.setupBookmarkFolders();
        });
        
        // Enter key handling
        usernameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                form.dispatchEvent(new Event('submit'));
            }
        });
    }

    showProgress() {
        const progressDiv = document.getElementById('welcome-progress');
        progressDiv.classList.add('show');
    }

    updateProgress(message, progress) {
        const progressText = document.getElementById('progress-text');
        const progressFill = document.getElementById('progress-fill');
        
        if (progressText) {
            progressText.textContent = message;
        }
        
        if (progressFill) {
            progressFill.style.width = `${progress}%`;
        }
    }

    async setupBookmarkFolders() {
        try {
            // Check if we have bookmark API access
            if (typeof chrome === 'undefined' || !chrome.bookmarks) {
                this.updateProgress(this.getText('welcome.noBookmarkAccess'), 100);
                await this.delay(2000);
                this.completeSetup();
                return;
            }

            // Get bookmark tree
            const bookmarkTree = await new Promise((resolve) => {
                chrome.bookmarks.getTree(resolve);
            });

            // Find or create "Other bookmarks" folder
            const otherBookmarks = this.findOtherBookmarksFolder(bookmarkTree);
            if (!otherBookmarks) {
                throw new Error('Cannot find Other Bookmarks folder');
            }

            // Check and create required folders
            for (let i = 0; i < this.requiredFolders.length; i++) {
                const folderName = this.requiredFolders[i];
                const progress = ((i + 1) / this.requiredFolders.length) * 100;
                
                const message = this.getText('welcome.creatingFolder');
                this.updateProgress(
                    message.replace('{folder}', folderName),
                    progress
                );
                
                await this.ensureFolderExists(otherBookmarks.id, folderName);
                await this.delay(500); // Visual delay for better UX
            }

            this.updateProgress(this.getText('welcome.setupComplete'), 100);
            await this.delay(1000);
            this.completeSetup();

        } catch (error) {
            console.error('Setup error:', error);
            this.updateProgress(this.getText('welcome.setupError'), 100);
            await this.delay(2000);
            this.completeSetup();
        }
    }

    findOtherBookmarksFolder(bookmarkTree) {
        const searchNames = ['Other bookmarks', '其他書籤', 'その他のブックマーク', 'Autres favoris'];
        
        const findFolder = (nodes) => {
            for (const node of nodes) {
                if (node.children && searchNames.includes(node.title)) {
                    return node;
                }
                if (node.children) {
                    const found = findFolder(node.children);
                    if (found) return found;
                }
            }
            return null;
        };
        
        return findFolder(bookmarkTree);
    }

    async ensureFolderExists(parentId, folderName) {
        try {
            // Search for existing folder
            const children = await new Promise((resolve) => {
                chrome.bookmarks.getChildren(parentId, resolve);
            });
            
            const existingFolder = children.find(child => 
                !child.url && child.title === folderName
            );
            
            if (!existingFolder) {
                // Create new folder
                await new Promise((resolve) => {
                    chrome.bookmarks.create({
                        parentId: parentId,
                        title: folderName
                    }, resolve);
                });
            }
        } catch (error) {
            console.warn(`Failed to create folder ${folderName}:`, error);
        }
    }

    completeSetup() {
        // Mark as initialized
        localStorage.setItem('bookmark_app_initialized', 'true');

        // Fade out welcome screen
        const overlay = document.getElementById('welcome-overlay');
        if (overlay) {
            overlay.classList.add('fade-out');
            setTimeout(() => {
                overlay.remove();
                // Call completion callback or navigate to home
                if (this.onComplete && typeof this.onComplete === 'function') {
                    this.onComplete();
                } else {
                    this.navigateToHome();
                }
            }, 800);
        }
    }

    navigateToHome() {
        // Clear top content and show home page
        if (window.Dom && window.Dom.TopContent) {
            window.Dom.TopContent.innerHTML = "";
        }

        // Navigate to home page if Pages are available
        if (window.Pages && window.Pages.Home) {
            window.Pages.Home.AppendElement();

            // Also update toolbar state if available
            if (window.toolBar) {
                window.toolBar.curPage = "Home";
                // Update toolbar visual state
                const tools = document.querySelectorAll('.tool');
                tools.forEach(tool => tool.classList.remove('active'));
                const homeBtn = document.getElementById('homebtn');
                if (homeBtn) {
                    homeBtn.classList.add('active');
                }
            }
        }
    }

    showError(message) {
        // Simple error display - could be enhanced with better UI
        const progressText = document.getElementById('progress-text');
        if (progressText) {
            progressText.textContent = message;
            progressText.style.color = '#ff6b6b';
            setTimeout(() => {
                progressText.style.color = '';
            }, 3000);
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    getText(key) {
        if (window.Lang) {
            return window.Lang.t(key);
        }
        
        // Fallback values
        const fallbacks = {
            'welcome.title': '歡迎使用書籤管理器',
            'welcome.subtitle': '讓我們開始設定您的個人化書籤體驗',
            'welcome.nameLabel': '您的名稱',
            'welcome.namePlaceholder': '請輸入您的名稱',
            'welcome.startButton': '開始使用',
            'welcome.settingUp': '正在設定您的書籤資料夾...',
            'welcome.creatingFolder': '正在建立 {folder} 資料夾...',
            'welcome.setupComplete': '設定完成！',
            'welcome.setupError': '設定時發生錯誤，但您仍可以正常使用',
            'welcome.noBookmarkAccess': '無法存取書籤，請手動建立資料夾',
            'welcome.nameRequired': '請輸入您的名稱',
            'welcome.nameTooLong': '名稱不能超過20個字元'
        };
        
        return fallbacks[key] || key;
    }
}
