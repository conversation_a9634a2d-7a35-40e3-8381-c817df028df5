// 繁體中文語言包
const zh = {
    // 通用
    common: {
        home: '首頁',
        learn: '學習',
        game: '遊戲',
        video: '影片',
        job: '工作',
        settings: '設定',
        search: '搜尋',
        add: '新增',
        edit: '編輯',
        delete: '刪除',
        save: '儲存',
        cancel: '取消',
        confirm: '確定',
        back: '返回',
        loading: '載入中...',
        error: '錯誤',
        success: '成功',
        warning: '警告',
        info: '資訊'
    },

    // 首頁
    home: {
        welcome: '歡迎使用書籤管理器',
        welcomeMessage: '{0}的書籤導航',
        searchPlaceholder: '搜尋Google...',
        quickAccess: '快速存取',
        recentBookmarks: '最近書籤'
    },

    // Google 頁面
    google: {
        title: 'Google 應用',
        apps: {
            gmail: 'Gmail',
            youtube: 'Youtube',
            maps: '地圖',
            drive: '雲端硬碟',
            meet: 'Meet',
            calendar: '日曆',
            chromeStore: 'Chrome商店',
            gemini: 'Gemini',
            play: 'Play',
            forms: 'Google 表單',
            translation: '翻譯'
        }
    },

    // FJU 頁面
    fju: {
        title: '輔仁大學',
        login: '登入',
        fee: '學費',
        back: '返回',
        loginOptions: {
            dataManagement: '個資管理系統',
            generalLogin: '一般登入'
        },
        feeOptions: {
            paymentPortal: '繳費入口',
            loanPortal: '學貸入口'
        }
    },

    // Tool 頁面
    tool: {
        title: '工具箱',
        loading: '載入工具中...',
        accessTitle: '書籤存取',
        accessText: '需要在支援 WebExtension 的瀏覽器中以擴充功能載入此頁面才能讀取書籤中的工具資料夾。',
        fallbackText: '以下是一些推薦的線上工具：',
        errorTitle: '載入錯誤',
        errorText: '無法載入工具書籤，請檢查瀏覽器權限設定。',
        noToolsTitle: '沒有找到工具',
        noToolsText: '請在書籤中創建一個名為 "tool" 或 "工具" 的資料夾，並添加你的工具網站。',
        tools: {
            colorPicker: '顏色選擇器',
            colorPickerDesc: '選擇和生成顏色代碼',
            qrGenerator: 'QR碼生成器',
            qrGeneratorDesc: '生成自訂QR碼',
            jsonFormatter: 'JSON格式化',
            jsonFormatterDesc: '格式化和驗證JSON數據',
            base64Encoder: 'Base64編碼',
            base64EncoderDesc: '編碼和解碼Base64字符串',
            urlShortener: '短網址',
            urlShortenerDesc: '縮短長網址',
            passwordGenerator: '密碼生成器',
            passwordGeneratorDesc: '生成安全密碼',
            imageCompressor: '圖片壓縮',
            imageCompressorDesc: '壓縮圖片大小',
            regexTester: '正則表達式測試',
            regexTesterDesc: '測試和調試正則表達式',
            markdownEditor: 'Markdown編輯器',
            markdownEditorDesc: '在線Markdown編輯和預覽',
            calculator: '計算器',
            calculatorDesc: '在線科學計算器',
            unitConverter: '單位轉換',
            unitConverterDesc: '各種單位轉換工具',
            codeBeautifier: '代碼美化',
            codeBeautifierDesc: '格式化和美化代碼'
        }
    },

    // 書籤管理
    bookmarks: {
        title: '瀏覽器書籤',
        searchPlaceholder: '搜尋書籤標題或網址...',
        addBookmark: '新增書籤',
        addFolder: '新增資料夾',
        addCurrentTab: '新增目前分頁',
        addCustomBookmark: '新增自訂書籤',
        editBookmark: '編輯書籤',
        deleteBookmark: '刪除書籤',
        renameFolder: '重新命名',
        moveItem: '移動',
        openBookmark: '開啟',
        bookmarkTitle: '書籤標題',
        bookmarkUrl: '書籤網址',
        customIconUrl: '自訂圖標網址',
        folderName: '資料夾名稱',
        enterUrl: '輸入網址 URL',
        enterTitle: '輸入標題 (可留空)',
        enterFolderName: '輸入資料夾名稱',
        enterNewName: '輸入新名稱',
        enterTargetFolder: '輸入目標資料夾名稱 (留空表示移到根目錄)',
        iconPreview: '圖標預覽將在此顯示',
        iconLoadError: '無法載入圖標',
        dropHere: '放置於此',
        backToParent: '返回上一層',
        untitled: '未命名',
        unnamedFolder: '(未命名資料夾)',
        categories: {
            folder: '資料夾',
            video: '影音',
            development: '程式/開發',
            google: 'Google',
            reading: '閱讀',
            school: '學校',
            other: '其他'
        }
    },

    // 書籤頁面
    bookmarks: {
        title: '書籤',
        search: '搜尋書籤...',
        addBookmark: '新增書籤',
        addFolder: '新增資料夾',
        editBookmark: '編輯書籤',
        renameFolder: '重新命名',
        moveItem: '移動',
        deleteItem: '刪除',
        cancel: '取消',
        confirm: '確定',
        save: '儲存',
        open: '開啟',

        // 表單標籤
        form: {
            title: '標題',
            url: '網址',
            icon: '自訂圖標網址',
            folderName: '資料夾名稱',
            titlePlaceholder: '輸入書籤標題',
            urlPlaceholder: 'https://example.com',
            iconPlaceholder: 'https://example.com/icon.png (選填)',
            folderPlaceholder: '輸入資料夾名稱'
        },

        // 提示訊息
        messages: {
            unnamedFolder: '(未命名資料夾)',
            deleteBookmarkTitle: '刪除書籤',
            deleteFolderTitle: '刪除資料夾'
        },

        // 錯誤訊息
        errors: {
            addError: '新增書籤時發生錯誤',
            editError: '更新書籤時發生錯誤'
        },

        // 移動功能
        move: {
            moveItemDesc: '選擇要將 "{item}" 移動到的資料夾',
            searchFolders: '搜尋資料夾...',
            moveHere: '移動到這裡',
            moveError: '移動失敗，請稍後再試'
        }
    },

    // 歡迎頁面
    welcome: {
        title: '歡迎使用書籤管理器',
        subtitle: '讓我們開始設定您的個人化書籤體驗',
        nameLabel: '您的名稱',
        namePlaceholder: '請輸入您的名稱',
        startButton: '開始使用',
        settingUp: '正在設定您的書籤資料夾...',
        creatingFolder: '正在建立 {folder} 資料夾...',
        setupComplete: '設定完成！',
        setupError: '設定時發生錯誤，但您仍可以正常使用',
        noBookmarkAccess: '無法存取書籤，請手動建立資料夾',
        nameRequired: '請輸入您的名稱',
        nameTooLong: '名稱不能超過20個字元'
    },

    // 設定頁面
    settings: {
        title: '設定',
        subtitle: '自訂您的書籤應用體驗',

        // 一般設定
        general: {
            title: '一般設定',
            username: '使用者名稱',
            usernameDesc: '顯示在應用中的名稱',
            defaultPage: '預設頁面',
            defaultPageDesc: '應用啟動時顯示的頁面',
            language: '語言',
            languageDesc: '應用介面語言'
        },





        // 資料管理
        data: {
            title: '資料管理',
            exportSettings: '匯出設定',
            exportSettingsDesc: '將所有設定匯出為檔案',
            importSettings: '匯入設定',
            importSettingsDesc: '從檔案匯入設定',
            resetSettings: '重置設定',
            resetSettingsDesc: '將所有設定恢復為預設值',
            clearAllData: '清除所有資料',
            clearAllDataDesc: '清除所有自訂圖標、排序和設定'
        },

        // 選項值
        options: {
            languages: {
                zh: '繁體中文',
                en: 'English',
                ja: '日本語'
            },
            pages: {
                Home: '首頁',
                Learn: '學習',
                Game: '遊戲',
                Video: '影片',
                Job: '工作'
            }
        }
    },

    // 訊息提示
    messages: {
        settingsExported: '設定已匯出',
        settingsImported: '設定已匯入',
        settingsReset: '設定已重置',
        dataCleared: '所有資料已清除',
        importFailed: '匯入失敗：檔案格式錯誤',
        confirmDelete: '確定要刪除 "{0}" 嗎？',
        confirmReset: '確定要重置所有設定嗎？此操作無法復原。',
        confirmClearData: '確定要清除所有資料嗎？這將刪除所有自訂圖標、排序和設定，此操作無法復原。',
        folderNotFound: '找不到指定的資料夾',
        targetFolderMustBeInScope: '目標資料夾必須在 {0} 範圍內',
        cannotGetCurrentTab: '無法取得目前分頁 (缺少 tabs 權限或瀏覽器不支援)。',
        bookmarksApiRequired: '需要在支援 WebExtension 的瀏覽器中以擴充功能載入此頁面才能同步書籤 (需 bookmarks 權限)。',
        bookmarksLoadError: '讀取書籤時發生錯誤：{0}'
    }
};

// 導出語言包
if (typeof module !== 'undefined' && module.exports) {
    module.exports = zh;
} else {
    window.zh = zh;
}
