/* Apple-style Welcome Screen - Light Theme */
#welcome-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    opacity: 1;
    transition: opacity 0.8s ease, transform 0.8s ease;
}

#welcome-overlay.fade-out {
    opacity: 0;
    transform: scale(1.1);
    pointer-events: none;
}

.welcome-container {
    text-align: center;
    color: #1d1d1f;
    max-width: 500px;
    padding: 50px;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.8);
    animation: welcomeSlideIn 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.welcome-logo {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 24px;
    opacity: 0;
    animation: logoFadeIn 1s ease 0.3s forwards;
    letter-spacing: -2px;
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
    opacity: 0;
    animation: titleFadeIn 1s ease 0.6s forwards;
    letter-spacing: -1.5px;
    color: #1d1d1f;
}

.welcome-subtitle {
    font-size: 1.1rem;
    font-weight: 400;
    margin-bottom: 40px;
    opacity: 0;
    animation: subtitleFadeIn 1s ease 0.9s forwards;
    line-height: 1.6;
    color: #86868b;
}

.welcome-form {
    opacity: 0;
    animation: formFadeIn 1s ease 1.2s forwards;
}

.welcome-input-group {
    margin-bottom: 30px;
}

.welcome-label {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: #1d1d1f;
    text-align: left;
}

.welcome-input {
    width: 100%;
    padding: 16px 20px;
    font-size: 1rem;
    border: 2px solid #e5e5e7;
    border-radius: 12px;
    background: #ffffff;
    color: #1d1d1f;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    font-weight: 500;
}

.welcome-input::placeholder {
    color: #86868b;
    font-weight: 400;
}

.welcome-input:focus {
    outline: none;
    border-color: #007AFF;
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
    transform: translateY(-1px);
}

.welcome-button {
    width: 100%;
    padding: 16px 24px;
    font-size: 1rem;
    font-weight: 600;
    border: none;
    border-radius: 12px;
    background: #007AFF;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
    margin-top: 8px;
}

.welcome-button:hover {
    background: #0056CC;
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 122, 255, 0.4);
}

.welcome-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(0, 122, 255, 0.3);
}

.welcome-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: #86868b;
    box-shadow: none;
}

.welcome-progress {
    margin-top: 30px;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.welcome-progress.show {
    opacity: 1;
}

.progress-text {
    font-size: 0.9rem;
    margin-bottom: 16px;
    color: #86868b;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: #f2f2f7;
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007AFF 0%, #5856D6 100%);
    border-radius: 2px;
    width: 0%;
    transition: width 0.5s ease;
}

/* Animations */
@keyframes welcomeSlideIn {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes logoFadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes titleFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes subtitleFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes formFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-container {
        max-width: 90%;
        padding: 40px 30px;
        border-radius: 20px;
    }

    .welcome-logo {
        font-size: 3.5rem;
    }

    .welcome-title {
        font-size: 2.2rem;
    }

    .welcome-subtitle {
        font-size: 1rem;
    }

    .welcome-input,
    .welcome-button {
        padding: 14px 18px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .welcome-container {
        padding: 30px 20px;
        border-radius: 16px;
        max-width: 95%;
    }

    .welcome-logo {
        font-size: 3rem;
    }

    .welcome-title {
        font-size: 2rem;
    }

    .welcome-subtitle {
        font-size: 0.95rem;
        margin-bottom: 32px;
    }

    .welcome-input,
    .welcome-button {
        padding: 14px 16px;
        font-size: 0.95rem;
    }
}

/* Dark theme support */
.theme-dark #welcome-overlay {
    background: linear-gradient(135deg, #1c1c1e 0%, #2c2c2e 100%);
}

.theme-dark .welcome-container {
    background: rgba(28, 28, 30, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #f2f2f7;
}

.theme-dark .welcome-title {
    color: #f2f2f7;
}

.theme-dark .welcome-subtitle {
    color: #8e8e93;
}

.theme-dark .welcome-label {
    color: #f2f2f7;
}

.theme-dark .welcome-input {
    background: #1c1c1e;
    border-color: #38383a;
    color: #f2f2f7;
}

.theme-dark .welcome-input::placeholder {
    color: #8e8e93;
}

.theme-dark .welcome-input:focus {
    border-color: #007AFF;
    background: #2c2c2e;
}

.theme-dark .progress-text {
    color: #8e8e93;
}

.theme-dark .progress-bar {
    background: #38383a;
}
