var Dom = {
    TopContent: document.getElementById("top-area"),
    ToolBarContainer: document.getElementById("toolbar"),
    SettingBtn: document.getElementById("settingbtn")
}

let toolBar;
let setting;
let Pages;

async function init(defaultPage) {
    // Initialize classes after they are loaded
    toolBar = new ToolBar();
    setting = new Setting();

    // Global settings manager
    window.AppSettings = setting;

    // Initialize language system
    const savedLanguage = setting.getSetting('language') || 'zh';
    await window.Lang.init(savedLanguage);

    // Initialize Pages first (before welcome screen)
    Pages = {
        Home: new Home(),
        FJU: new FJU(),
        Google: new Google(),
        Learn: new Learn('Learn'),
        Game: new Learn('Game'),
        Video: new Learn('Video'),
        Job: new Learn('Job'),
        Tool: new Learn('Tool'),
        Setting: setting
    };

    // Make Pages and toolBar globally available for Welcome screen
    window.Pages = Pages;
    window.toolBar = toolBar;

    // Check for first-time user and show welcome screen
    const welcome = new Welcome(async () => {
        // Callback function to execute after welcome screen completes
        Dom.TopContent.innerHTML = "";
        Pages.Home.AppendElement();
        toolBar.curPage = "Home";

        // Update toolbar visual state
        const tools = document.querySelectorAll('.tool');
        tools.forEach(tool => tool.classList.remove('active'));
        const homeBtn = document.getElementById('homebtn');
        if (homeBtn) {
            homeBtn.classList.add('active');
        }

        // Apply settings after welcome completion
        await setting.applySettings();
    });

    const isFirstTime = await welcome.show();

    // Only show default page if not first time
    if (!isFirstTime) {
        Dom.TopContent.innerHTML = "";
        if (defaultPage === "Home") {
            Pages.Home.AppendElement();
        }
        // Apply settings for non-first-time users
        await setting.applySettings();
    }

    toolBar.PageBind();

    // Add event listener for setting button (outside toolbar)
    if (Dom.SettingBtn) {
        Dom.SettingBtn.addEventListener('click', async () => {
            console.log('Setting button clicked'); // Debug log
            toolBar.curPage = "Setting";
            await toolBar.ChangePage("Setting");
        });
        console.log('Setting button event listener added'); // Debug log
    } else {
        console.error('Setting button not found!'); // Debug log
    }
}

// Initialize with default page after DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing app...'); // Debug log
    init("Home");
});

// Fallback initialization if DOMContentLoaded already fired
if (document.readyState === 'loading') {
    // DOM is still loading
} else {
    // DOM is already loaded
    console.log('DOM already loaded, initializing app immediately...'); // Debug log
    init("Home");
}

