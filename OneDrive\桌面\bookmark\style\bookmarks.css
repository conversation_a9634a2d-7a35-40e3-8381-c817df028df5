/* Apple-style Bookmarks Design */
#bookmarks-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f7;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Fixed Navigation Header */
#bookmarks-nav {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 100;
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 12px;
  padding: 8px 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

#back-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #007AFF;
  border-radius: 8px;
  color: white;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

#back-btn:hover {
  background: #0056CC;
  transform: scale(1.05);
}

#back-btn:disabled {
  background: #E5E5EA;
  color: #8E8E93;
  cursor: not-allowed;
  transform: none;
}

#nav-title {
  font-size: 17px;
  font-weight: 600;
  color: #1D1D1F;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Main Content Area */
#bookmarks-content {
  flex: 1;
  padding: 100px 40px 40px 40px;
  overflow-y: auto;
}

/* Action Bar */
#bookmarks-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

#bm-search {
  flex: 1;
  max-width: 400px;
  height: 44px;
  padding: 0 16px;
  border: none;
  outline: none;
  border-radius: 22px;
  background: white;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

#bm-search:focus {
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.2);
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.bm-btn {
  height: 44px;
  padding: 0 20px;
  border: none;
  border-radius: 22px;
  background: #007AFF;
  color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.bm-btn:hover {
  background: #0056CC;
  transform: translateY(-1px);
}

.bm-btn.secondary {
  background: white;
  color: #007AFF;
  border: 1px solid #E5E5EA;
}

.bm-btn.secondary:hover {
  background: #F2F2F7;
}

/* Grid Layout */
#bookmarks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 24px;
  padding: 20px 0;
}

/* Bookmark Items */
.bookmark-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 16px;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  position: relative;
  user-select: none;
  min-height: 140px;
}

.bookmark-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.bookmark-item.selected {
  background: #E3F2FD;
  border: 2px solid #007AFF;
}

.bookmark-icon {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  margin-bottom: 16px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-color: #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  flex-shrink: 0;
}

.bookmark-icon.folder {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23007AFF"><path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"/></svg>');
  background-size: 48px 48px;
}

.bookmark-icon.default {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23007AFF"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>');
  background-size: 40px 40px;
}

.bookmark-title {
  font-size: 15px;
  font-weight: 500;
  color: #1D1D1F;
  text-align: center;
  line-height: 1.4;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
}

/* Context Menu */
.bookmark-menu {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 28px;
  height: 28px;
  border: none;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 14px;
  color: #666;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.bookmark-item:hover .bookmark-menu {
  opacity: 1;
}

.bookmark-menu:hover {
  background: rgba(0, 0, 0, 0.2);
  color: #333;
  transform: scale(1.1);
}

/* Context Menu Dropdown */
.context-menu {
  position: absolute;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  padding: 8px 0;
  min-width: 160px;
  z-index: 1000;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.context-menu-item {
  padding: 12px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #1D1D1F;
  transition: background 0.1s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
}

.context-menu-item:hover {
  background: #F2F2F7;
}

.context-menu-item.danger {
  color: #FF3B30;
}

.context-menu-item.danger:hover {
  background: #FFEBEE;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal {
  background: white;
  border-radius: 16px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.3);
}

.modal.advanced-modal {
  max-width: 500px;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #1D1D1F;
  margin-bottom: 20px;
}

.modal-field {
  margin-bottom: 20px;
}

.modal-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #1D1D1F;
  margin-bottom: 6px;
}

.modal-input {
  width: 100%;
  height: 44px;
  padding: 0 16px;
  border: 1px solid #E5E5EA;
  border-radius: 8px;
  font-size: 16px;
  outline: none;
  transition: border-color 0.2s ease;
}

.modal-input:focus {
  border-color: #007AFF;
}

.icon-preview {
  margin-top: 8px;
}

.icon-preview-img {
  border: 1px solid #E5E5EA;
  background: #F2F2F7;
}

.icon-preview-text {
  font-style: italic;
}

/* Drag and Drop Styles */
.bookmark-item.dragging {
  opacity: 0.8;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.3);
  border: 2px solid #007AFF;
  background: #E3F2FD;
}

.drag-placeholder {
  background: rgba(0, 122, 255, 0.2);
  border: 2px dashed #007AFF;
  border-radius: 20px;
  margin: 0;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 140px;
  box-sizing: border-box;
}

.drag-placeholder::before {
  content: "放置於此";
  color: #007AFF;
  font-size: 14px;
  font-weight: 500;
  opacity: 0.8;
}

.dragging-active {
  cursor: grabbing !important;
}

.dragging-active * {
  cursor: grabbing !important;
}

.bookmark-item:not(.dragging) {
  transition: transform 0.2s ease;
}

/* Long press visual feedback */
.bookmark-item.long-press-active {
  transform: scale(0.95);
  opacity: 0.9;
}

.modal-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.modal-btn {
  height: 44px;
  padding: 0 20px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-btn.primary {
  background: #007AFF;
  color: white;
}

.modal-btn.primary:hover {
  background: #0056CC;
}

.modal-btn.secondary {
  background: #F2F2F7;
  color: #1D1D1F;
}

.modal-btn.secondary:hover {
  background: #E5E5EA;
}

/* Warning Message */
.bm-warning {
  padding: 20px;
  background: #FFF3CD;
  border: 1px solid #FFEAA7;
  border-radius: 12px;
  color: #856404;
  margin: 20px;
  text-align: center;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
  #bookmarks-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 20px;
  }

  .bookmark-icon {
    width: 72px;
    height: 72px;
  }

  .bookmark-title {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  #bookmarks-content {
    padding: 80px 16px 20px 16px;
  }

  #bookmarks-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 16px;
  }

  .bookmark-item {
    padding: 16px 12px;
    min-height: 120px;
  }

  .bookmark-icon {
    width: 64px;
    height: 64px;
    margin-bottom: 12px;
  }

  .bookmark-icon.folder {
    background-size: 40px 40px;
  }

  .bookmark-icon.default {
    background-size: 32px 32px;
  }

  .bookmark-title {
    font-size: 13px;
  }

  #bookmarks-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .action-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .bm-btn {
    flex: 1;
    min-width: 120px;
  }

  #bm-search {
    max-width: none;
  }
}

@media (max-width: 480px) {
  #bookmarks-nav {
    left: 12px;
    top: 12px;
    padding: 6px 12px;
  }

  #nav-title {
    font-size: 15px;
    max-width: 200px;
  }

  #back-btn {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  #bookmarks-content {
    padding: 70px 12px 16px 12px;
  }

  #bookmarks-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
  }

  .bookmark-item {
    padding: 12px 8px;
    min-height: 100px;
    border-radius: 16px;
  }

  .bookmark-icon {
    width: 56px;
    height: 56px;
    margin-bottom: 8px;
    border-radius: 12px;
  }

  .bookmark-icon.folder {
    background-size: 32px 32px;
  }

  .bookmark-icon.default {
    background-size: 28px 28px;
  }

  .bookmark-title {
    font-size: 12px;
    line-height: 1.3;
  }

  .bookmark-menu {
    width: 24px;
    height: 24px;
    top: 8px;
    right: 8px;
    font-size: 12px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .bm-btn {
    height: 40px;
    font-size: 14px;
  }

  #bm-search {
    height: 40px;
    font-size: 14px;
  }
}

@media (min-width: 1400px) {
  #bookmarks-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 28px;
  }

  .bookmark-item {
    padding: 24px 20px;
    min-height: 160px;
  }

  .bookmark-icon {
    width: 96px;
    height: 96px;
    margin-bottom: 20px;
  }

  .bookmark-icon.folder {
    background-size: 56px 56px;
  }

  .bookmark-icon.default {
    background-size: 48px 48px;
  }

  .bookmark-title {
    font-size: 16px;
  }
}

/* Enhanced Responsive Design for Bookmarks */
@media (max-width: 1024px) {
  #bookmarks-nav {
    top: 15px;
    left: 15px;
    padding: 6px 12px;
  }

  #nav-title {
    font-size: 16px;
  }

  #bookmarks-actions {
    padding: 15px 20px;
    gap: 12px;
  }

  #bm-search {
    height: 40px;
    font-size: 15px;
  }

  .bm-btn {
    padding: 10px 16px;
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  #bookmarks-nav {
    top: 10px;
    left: 10px;
    right: 10px;
    width: auto;
    padding: 8px 12px;
  }

  #back-btn {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  #nav-title {
    font-size: 15px;
  }

  #bookmarks-content {
    padding-top: 70px;
  }

  #bookmarks-actions {
    flex-direction: column;
    padding: 12px 15px;
    gap: 10px;
  }

  .action-buttons {
    width: 100%;
    justify-content: space-between;
  }

  #bm-search {
    width: 100%;
    height: 38px;
    font-size: 14px;
  }

  .bm-btn {
    flex: 1;
    padding: 8px 12px;
    font-size: 13px;
  }

  #bookmarks-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 12px;
    padding: 0 15px 15px;
  }
}

@media (max-width: 480px) {
  #bookmarks-nav {
    top: 5px;
    left: 5px;
    right: 5px;
    padding: 6px 10px;
    border-radius: 10px;
  }

  #back-btn {
    width: 26px;
    height: 26px;
    font-size: 12px;
    border-radius: 6px;
  }

  #nav-title {
    font-size: 14px;
  }

  #bookmarks-content {
    padding-top: 60px;
  }

  #bookmarks-actions {
    padding: 10px 12px;
    gap: 8px;
  }

  #bm-search {
    height: 36px;
    font-size: 13px;
    border-radius: 8px;
  }

  .bm-btn {
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 6px;
  }

  #bookmarks-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    padding: 0 12px 12px;
  }

  .bookmark-item {
    padding: 16px 12px;
    min-height: 120px;
    border-radius: 12px;
  }

  .bookmark-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 12px;
  }

  .bookmark-title {
    font-size: 13px;
    line-height: 1.3;
  }
}

@media (max-width: 360px) {
  #bookmarks-nav {
    padding: 5px 8px;
  }

  #nav-title {
    font-size: 13px;
  }

  #bookmarks-actions {
    padding: 8px 10px;
  }

  #bm-search {
    height: 34px;
    font-size: 12px;
  }

  .bm-btn {
    padding: 5px 8px;
    font-size: 11px;
  }

  #bookmarks-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
    padding: 0 10px 10px;
  }

  .bookmark-item {
    padding: 12px 8px;
    min-height: 100px;
    border-radius: 10px;
  }

  .bookmark-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 10px;
  }

  .bookmark-title {
    font-size: 12px;
  }
}

@media (max-width: 320px) {
  #bookmarks-nav {
    padding: 4px 6px;
  }

  #back-btn {
    width: 24px;
    height: 24px;
    font-size: 11px;
  }

  #nav-title {
    font-size: 12px;
  }

  #bookmarks-grid {
    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
    gap: 6px;
    padding: 0 8px 8px;
  }

  .bookmark-item {
    padding: 10px 6px;
    min-height: 90px;
  }

  .bookmark-icon {
    width: 45px;
    height: 45px;
    margin-bottom: 8px;
  }

  .bookmark-title {
    font-size: 11px;
  }
}

/* Move Modal Styles */
.move-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

.move-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9);
  animation: modalSlideIn 0.3s ease forwards;
}

.move-modal-header {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.move-modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.move-modal-subtitle {
  font-size: 14px;
  color: #86868b;
  margin: 0;
}

.move-modal-content {
  padding: 20px 24px;
  max-height: 400px;
  overflow-y: auto;
}

.move-search {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e5e7;
  border-radius: 10px;
  font-size: 16px;
  margin-bottom: 16px;
  transition: border-color 0.3s ease;
}

.move-search:focus {
  outline: none;
  border-color: #007AFF;
}

.move-folders-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.move-folder-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.move-folder-item:hover {
  background: #f8f9fa;
  border-color: #e5e5e7;
}

.move-folder-item.selected {
  background: #007AFF;
  color: white;
  border-color: #007AFF;
}

.move-folder-item.current {
  background: #f0f0f0;
  color: #86868b;
  cursor: not-allowed;
}

.move-folder-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.move-folder-info {
  flex: 1;
}

.move-folder-name {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 2px 0;
}

.move-folder-path {
  font-size: 12px;
  opacity: 0.7;
  margin: 0;
}

.move-folder-item.selected .move-folder-path {
  opacity: 0.8;
}

.move-modal-actions {
  padding: 16px 24px 24px;
  display: flex;
  gap: 12px;
  border-top: 1px solid #f0f0f0;
}

.move-modal-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.move-modal-btn.cancel {
  background: #f0f0f0;
  color: #86868b;
}

.move-modal-btn.cancel:hover {
  background: #e5e5e7;
}

.move-modal-btn.confirm {
  background: #007AFF;
  color: white;
}

.move-modal-btn.confirm:hover {
  background: #0056CC;
}

.move-modal-btn.confirm:disabled {
  background: #c7c7cc;
  cursor: not-allowed;
}

/* Dark theme support */
.theme-dark .move-modal {
  background: #1c1c1e;
}

.theme-dark .move-modal-header {
  border-bottom-color: #38383a;
}

.theme-dark .move-modal-title {
  color: #f2f2f7;
}

.theme-dark .move-modal-subtitle {
  color: #8e8e93;
}

.theme-dark .move-search {
  background: #2c2c2e;
  border-color: #38383a;
  color: #f2f2f7;
}

.theme-dark .move-search:focus {
  border-color: #007AFF;
}

.theme-dark .move-folder-item:hover {
  background: #2c2c2e;
  border-color: #38383a;
}

.theme-dark .move-folder-item.current {
  background: #38383a;
  color: #8e8e93;
}

.theme-dark .move-modal-actions {
  border-top-color: #38383a;
}

.theme-dark .move-modal-btn.cancel {
  background: #38383a;
  color: #8e8e93;
}

.theme-dark .move-modal-btn.cancel:hover {
  background: #48484a;
}

/* Animations */
@keyframes fadeIn {
  to { opacity: 1; }
}

@keyframes modalSlideIn {
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .move-modal {
    width: 95%;
    max-height: 85vh;
  }

  .move-modal-header {
    padding: 20px 20px 12px;
  }

  .move-modal-content {
    padding: 16px 20px;
  }

  .move-modal-actions {
    padding: 12px 20px 20px;
  }
}

