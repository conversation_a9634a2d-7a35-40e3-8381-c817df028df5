// 語言管理器
class Lang {
    constructor() {
        this.currentLang = 'zh'; // 預設語言
        this.languages = {};
        this.loadedLanguages = new Set();
    }

    // 載入語言包
    async loadLanguage(langCode) {
        if (this.loadedLanguages.has(langCode)) {
            return this.languages[langCode];
        }

        try {
            // 動態載入語言腳本
            await this.loadScript(`./lang/${langCode}.js`);
            
            // 獲取語言包
            const langPack = window[langCode];
            if (langPack) {
                this.languages[langCode] = langPack;
                this.loadedLanguages.add(langCode);
                return langPack;
            } else {
                throw new Error(`Language pack ${langCode} not found`);
            }
        } catch (error) {
            console.error(`Failed to load language ${langCode}:`, error);
            // 回退到預設語言
            if (langCode !== 'zh') {
                return await this.loadLanguage('zh');
            }
            return null;
        }
    }

    // 動態載入腳本
    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // 設定當前語言
    async setLanguage(langCode) {
        const langPack = await this.loadLanguage(langCode);
        if (langPack) {
            this.currentLang = langCode;
            this.languages[langCode] = langPack;
            return true;
        }
        return false;
    }

    // 獲取翻譯文字
    t(key, params = []) {
        const langPack = this.languages[this.currentLang];
        if (!langPack) {
            console.warn(`Language pack ${this.currentLang} not loaded`);
            return key;
        }

        // 支援巢狀鍵值，如 'settings.general.title'
        const keys = key.split('.');
        let value = langPack;
        
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                console.warn(`Translation key not found: ${key}`);
                return key;
            }
        }

        // 如果是字串，進行參數替換
        if (typeof value === 'string') {
            return this.formatString(value, params);
        }

        return value || key;
    }

    // 字串格式化，支援 {0}, {1} 等參數
    formatString(str, params) {
        if (!params || params.length === 0) {
            return str;
        }

        return str.replace(/\{(\d+)\}/g, (match, index) => {
            const paramIndex = parseInt(index);
            return paramIndex < params.length ? params[paramIndex] : match;
        });
    }

    // 獲取當前語言
    getCurrentLanguage() {
        return this.currentLang;
    }

    // 獲取可用語言列表
    getAvailableLanguages() {
        return [
            { code: 'zh', name: '繁體中文' },
            { code: 'en', name: 'English' },
            { code: 'ja', name: '日本語' }
        ];
    }

    // 初始化語言系統
    async init(langCode = 'zh') {
        await this.setLanguage(langCode);
        return this.languages[this.currentLang];
    }

    // 更新頁面文字
    updatePageTexts() {
        // 更新所有帶有 data-lang 屬性的元素
        const elements = document.querySelectorAll('[data-lang]');
        elements.forEach(element => {
            const key = element.getAttribute('data-lang');
            const text = this.t(key);
            
            if (element.tagName === 'INPUT' && element.type === 'text') {
                element.placeholder = text;
            } else {
                element.textContent = text;
            }
        });

        // 更新所有帶有 data-lang-placeholder 屬性的元素
        const placeholderElements = document.querySelectorAll('[data-lang-placeholder]');
        placeholderElements.forEach(element => {
            const key = element.getAttribute('data-lang-placeholder');
            element.placeholder = this.t(key);
        });

        // 更新所有帶有 data-lang-title 屬性的元素
        const titleElements = document.querySelectorAll('[data-lang-title]');
        titleElements.forEach(element => {
            const key = element.getAttribute('data-lang-title');
            element.title = this.t(key);
        });
    }

    // 創建多語言元素
    createElement(tag, langKey, className = '') {
        const element = document.createElement(tag);
        if (className) element.className = className;
        element.setAttribute('data-lang', langKey);
        element.textContent = this.t(langKey);
        return element;
    }

    // 創建多語言輸入框
    createInput(type, langKey, className = '') {
        const input = document.createElement('input');
        input.type = type;
        if (className) input.className = className;
        input.setAttribute('data-lang-placeholder', langKey);
        input.placeholder = this.t(langKey);
        return input;
    }
}

// 創建全域語言管理器實例
window.Lang = new Lang();
