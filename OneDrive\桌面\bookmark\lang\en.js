// English Language Pack
const en = {
    // Common
    common: {
        home: 'Home',
        learn: 'Learn',
        game: 'Game',
        video: 'Video',
        job: 'Job',
        settings: 'Settings',
        search: 'Search',
        add: 'Add',
        edit: 'Edit',
        delete: 'Delete',
        save: 'Save',
        cancel: 'Cancel',
        confirm: 'Confirm',
        back: 'Back',
        loading: 'Loading...',
        error: 'Error',
        success: 'Success',
        warning: 'Warning',
        info: 'Info'
    },

    // Home
    home: {
        welcome: 'Welcome to Bookmark Manager',
        welcomeMessage: "{0}'s Bookmarks",
        searchPlaceholder: 'Search Google...',
        quickAccess: 'Quick Access',
        recentBookmarks: 'Recent Bookmarks'
    },

    // Google Page
    google: {
        title: 'Google Apps',
        apps: {
            gmail: 'Gmail',
            youtube: 'Youtube',
            maps: 'Maps',
            drive: 'Drive',
            meet: 'Meet',
            calendar: 'Calendar',
            chromeStore: 'Chrome Store',
            gemini: 'Gemini',
            play: 'Play',
            forms: 'Google Forms',
            translation: 'Translation'
        }
    },

    // FJU Page
    fju: {
        title: 'Fu Jen University',
        login: 'Login',
        fee: 'Tuition',
        back: 'Back',
        loginOptions: {
            dataManagement: 'Data Management System',
            generalLogin: 'General Login'
        },
        feeOptions: {
            paymentPortal: 'Payment Portal',
            loanPortal: 'Student Loan Portal'
        }
    },

    // Tool Page
    tool: {
        title: 'Toolbox',
        loading: 'Loading tools...',
        accessTitle: 'Bookmark Access',
        accessText: 'Need to load this page as extension in WebExtension-supported browser to read tool folder from bookmarks.',
        fallbackText: 'Here are some recommended online tools:',
        errorTitle: 'Loading Error',
        errorText: 'Unable to load tool bookmarks, please check browser permission settings.',
        noToolsTitle: 'No Tools Found',
        noToolsText: 'Please create a folder named "tool" or "tools" in your bookmarks and add your tool websites.',
        tools: {
            colorPicker: 'Color Picker',
            colorPickerDesc: 'Pick and generate color codes',
            qrGenerator: 'QR Code Generator',
            qrGeneratorDesc: 'Generate custom QR codes',
            jsonFormatter: 'JSON Formatter',
            jsonFormatterDesc: 'Format and validate JSON data',
            base64Encoder: 'Base64 Encoder',
            base64EncoderDesc: 'Encode and decode Base64 strings',
            urlShortener: 'URL Shortener',
            urlShortenerDesc: 'Shorten long URLs',
            passwordGenerator: 'Password Generator',
            passwordGeneratorDesc: 'Generate secure passwords',
            imageCompressor: 'Image Compressor',
            imageCompressorDesc: 'Compress image file sizes',
            regexTester: 'Regex Tester',
            regexTesterDesc: 'Test and debug regular expressions',
            markdownEditor: 'Markdown Editor',
            markdownEditorDesc: 'Online Markdown editor and preview',
            calculator: 'Calculator',
            calculatorDesc: 'Online scientific calculator',
            unitConverter: 'Unit Converter',
            unitConverterDesc: 'Various unit conversion tools',
            codeBeautifier: 'Code Beautifier',
            codeBeautifierDesc: 'Format and beautify code'
        }
    },

    // Bookmarks
    bookmarks: {
        title: 'Browser Bookmarks',
        searchPlaceholder: 'Search bookmarks...',
        addBookmark: 'Add Bookmark',
        addFolder: 'Add Folder',
        addCurrentTab: 'Add Current Tab',
        addCustomBookmark: 'Add Custom Bookmark',
        editBookmark: 'Edit Bookmark',
        deleteBookmark: 'Delete Bookmark',
        renameFolder: 'Rename',
        moveItem: 'Move',
        openBookmark: 'Open',
        bookmarkTitle: 'Bookmark Title',
        bookmarkUrl: 'Bookmark URL',
        customIconUrl: 'Custom Icon URL',
        folderName: 'Folder Name',
        enterUrl: 'Enter URL',
        enterTitle: 'Enter title (optional)',
        enterFolderName: 'Enter folder name',
        enterNewName: 'Enter new name',
        enterTargetFolder: 'Enter target folder name (empty for root)',
        iconPreview: 'Icon preview will show here',
        iconLoadError: 'Cannot load icon',
        dropHere: 'Drop here',
        backToParent: 'Back to parent',
        untitled: 'Untitled',
        unnamedFolder: '(Unnamed folder)',
        categories: {
            folder: 'Folder',
            video: 'Video',
            development: 'Development',
            google: 'Google',
            reading: 'Reading',
            school: 'School',
            other: 'Other'
        }
    },

    // Bookmarks Page
    bookmarks: {
        title: 'Bookmarks',
        search: 'Search bookmarks...',
        addBookmark: 'Add Bookmark',
        addFolder: 'Add Folder',
        editBookmark: 'Edit Bookmark',
        renameFolder: 'Rename',
        moveItem: 'Move',
        deleteItem: 'Delete',
        cancel: 'Cancel',
        confirm: 'Confirm',
        save: 'Save',
        open: 'Open',

        // Form labels
        form: {
            title: 'Title',
            url: 'URL',
            icon: 'Custom Icon URL',
            folderName: 'Folder Name',
            titlePlaceholder: 'Enter bookmark title',
            urlPlaceholder: 'https://example.com',
            iconPlaceholder: 'https://example.com/icon.png (optional)',
            folderPlaceholder: 'Enter folder name'
        },

        // Messages
        messages: {
            unnamedFolder: '(Unnamed Folder)',
            deleteBookmarkTitle: 'Delete Bookmark',
            deleteFolderTitle: 'Delete Folder'
        },

        // Error messages
        errors: {
            addError: 'Error occurred while adding bookmark',
            editError: 'Error occurred while updating bookmark'
        },

        // Move functionality
        move: {
            moveItemDesc: 'Select the folder to move "{item}" to',
            searchFolders: 'Search folders...',
            moveHere: 'Move Here',
            moveError: 'Move failed, please try again'
        }
    },

    // Welcome Page
    welcome: {
        title: 'Welcome to Bookmark Manager',
        subtitle: 'Let\'s set up your personalized bookmark experience',
        nameLabel: 'Your Name',
        namePlaceholder: 'Please enter your name',
        startButton: 'Get Started',
        settingUp: 'Setting up your bookmark folders...',
        creatingFolder: 'Creating {folder} folder...',
        setupComplete: 'Setup Complete!',
        setupError: 'Setup error occurred, but you can still use the app',
        noBookmarkAccess: 'Cannot access bookmarks, please create folders manually',
        nameRequired: 'Please enter your name',
        nameTooLong: 'Name cannot exceed 20 characters'
    },

    // Settings
    settings: {
        title: 'Settings',
        subtitle: 'Customize your bookmark app experience',
        
        // General
        general: {
            title: 'General Settings',
            username: 'Username',
            usernameDesc: 'Name displayed in the app',
            defaultPage: 'Default Page',
            defaultPageDesc: 'Page shown when app starts',
            language: 'Language',
            languageDesc: 'App interface language'
        },





        // Data
        data: {
            title: 'Data Management',
            exportSettings: 'Export Settings',
            exportSettingsDesc: 'Export all settings to file',
            importSettings: 'Import Settings',
            importSettingsDesc: 'Import settings from file',
            resetSettings: 'Reset Settings',
            resetSettingsDesc: 'Restore all settings to default',
            clearAllData: 'Clear All Data',
            clearAllDataDesc: 'Clear all custom icons, orders and settings'
        },

        // Options
        options: {
            languages: {
                zh: '繁體中文',
                en: 'English',
                ja: '日本語'
            },
            pages: {
                Home: 'Home',
                Learn: 'Learn',
                Game: 'Game',
                Video: 'Video',
                Job: 'Job'
            }
        }
    },

    // Messages
    messages: {
        settingsExported: 'Settings exported',
        settingsImported: 'Settings imported',
        settingsReset: 'Settings reset',
        dataCleared: 'All data cleared',
        importFailed: 'Import failed: Invalid file format',
        confirmDelete: 'Are you sure you want to delete "{0}"?',
        confirmReset: 'Are you sure you want to reset all settings? This action cannot be undone.',
        confirmClearData: 'Are you sure you want to clear all data? This will delete all custom icons, orders and settings. This action cannot be undone.',
        folderNotFound: 'Specified folder not found',
        targetFolderMustBeInScope: 'Target folder must be within {0} scope',
        cannotGetCurrentTab: 'Cannot get current tab (missing tabs permission or browser not supported).',
        bookmarksApiRequired: 'Need to load this page as extension in WebExtension-supported browser to sync bookmarks (requires bookmarks permission).',
        bookmarksLoadError: 'Error loading bookmarks: {0}'
    }
};

// Export language pack
if (typeof module !== 'undefined' && module.exports) {
    module.exports = en;
} else {
    window.en = en;
}
