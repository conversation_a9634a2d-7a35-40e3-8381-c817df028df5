class Learn {
  constructor(folderFilter = null) {
    this.flatIndex = new Map();
    this.searchText = "";
    this.folderFilter = folderFilter; // 'Game' | 'Video' | 'Learn' | null
    this.startFolderId = null;
    this.currentFolderId = null;
    this.rootFolderId = null; // The folder that acts as root (cannot go above this)
    this.customIcons = this.loadCustomIcons(); // Load custom icons from localStorage
    this.customOrder = this.loadCustomOrder(); // Load custom order from localStorage
    this.isDragging = false;
    this.dragStartTime = 0;
    this.longPressTimer = null;
    this.dragElement = null;
    this.dragPlaceholder = null;
  }

  AppendElement() {
    Dom.TopContent.innerHTML = "";

    // Main container
    const container = document.createElement('div');
    container.id = 'bookmarks-container';
    container.className = 'learn-container';
    container.setAttribute('data-page-type', this.folderFilter || 'Learn');

    // Fixed navigation header
    const nav = document.createElement('div');
    nav.id = 'bookmarks-nav';

    const backBtn = document.createElement('button');
    backBtn.id = 'back-btn';
    backBtn.innerHTML = '←';
    backBtn.addEventListener('click', () => this.goBack());

    const navTitle = document.createElement('div');
    navTitle.id = 'nav-title';
    navTitle.textContent = this.getText('title');

    nav.append(backBtn, navTitle);

    // Main content area
    const content = document.createElement('div');
    content.id = 'bookmarks-content';

    // Action bar
    const actions = document.createElement('div');
    actions.id = 'bookmarks-actions';

    const search = document.createElement('input');
    search.id = 'bm-search';
    search.placeholder = this.getText('search');
    search.addEventListener('input', () => {
      this.searchText = search.value.trim().toLowerCase();
      this.renderLevel();
    });

    const actionButtons = document.createElement('div');
    actionButtons.className = 'action-buttons';

    const addBtn = document.createElement('button');
    addBtn.className = 'bm-btn';
    addBtn.textContent = this.getText('addBookmark');
    addBtn.addEventListener('click', () => this.showAddModal());

    const addFolderBtn = document.createElement('button');
    addFolderBtn.className = 'bm-btn secondary';
    addFolderBtn.textContent = this.getText('addFolder');
    addFolderBtn.addEventListener('click', () => this.showAddFolderModal());

    actionButtons.append(addBtn, addFolderBtn);
    actions.append(search, actionButtons);

    // Grid container
    const grid = document.createElement('div');
    grid.id = 'bookmarks-grid';

    content.append(actions, grid);
    container.append(nav, content);
    Dom.TopContent.appendChild(container);

    this.gridContainer = grid;
    this.navTitle = navTitle;
    this.backBtn = backBtn;
    this.selectedItems = new Set();

    // Add window resize listener to update title lengths
    this.resizeHandler = () => this.renderLevel();
    window.addEventListener('resize', this.resizeHandler);

    this.initSync();
  }

  async initSync() {
    if (!this.hasBookmarksAPI()) {
      this.gridContainer.innerHTML = '';
      const warn = document.createElement('div');
      warn.className = 'bm-warning';
      warn.textContent = '需要在支援 WebExtension 的瀏覽器中以擴充功能載入此頁面才能同步書籤 (需 bookmarks 權限)。';
      this.gridContainer.appendChild(warn);
      return;
    }

    await this.loadAll();
    this.attachListeners();

    // Initialize current level
    if (this.folderFilter) {
      const folder = this.findFolderByExactTitle(this.folderFilter);
      if (folder) {
        this.startFolderId = folder.id;
        this.currentFolderId = this.startFolderId;
        this.rootFolderId = folder.id; // Set as root - cannot go above this
      } else {
        // If folder not found, create it
        try {
          const otherBookmarksFolder = this.findFolderByTitle(['Other bookmarks', '其他書籤', 'Others']);
          const parentId = otherBookmarksFolder ? otherBookmarksFolder.id : (this.roots && this.roots[0] ? this.roots[0].id : '1');
          const newFolder = await this.api.create({ parentId, title: this.folderFilter });
          this.startFolderId = newFolder.id;
          this.currentFolderId = this.startFolderId;
          this.rootFolderId = newFolder.id;
          // Reload to get the new folder in our index
          await this.loadAll();
        } catch (e) {
          this.showError(e);
          return;
        }
      }
    } else {
      // Start from bookmarks bar or first root
      const bookmarksBar = this.findFolderByTitle(['Bookmarks bar', '書籤列', 'Bookmarks Bar']);
      if (bookmarksBar) {
        this.startFolderId = bookmarksBar.id;
        this.currentFolderId = this.startFolderId;
        this.rootFolderId = null; // No restriction for general bookmarks view
      } else {
        const firstRoot = this.roots && this.roots[0];
        this.startFolderId = firstRoot ? firstRoot.id : null;
        this.currentFolderId = this.startFolderId;
        this.rootFolderId = null;
      }
    }

    this.renderLevel();
    this.updateNavigation();
  }

  hasBookmarksAPI() {
    return (typeof chrome !== 'undefined' && chrome.bookmarks) || (typeof browser !== 'undefined' && browser.bookmarks);
  }
  get api() {
    return typeof browser !== 'undefined' && browser.bookmarks ? browser.bookmarks : chrome.bookmarks;
  }

  async loadAll() {
    try {
      const roots = await this.api.getTree();
      this.roots = roots;
      this.rebuildIndex();
      this.renderLevel();
    } catch (e) { this.showError(e); }
  }

  rebuildIndex() {
    this.flatIndex.clear();
    const walk = (nodes) => { for (const n of nodes) { this.flatIndex.set(n.id, n); if (n.children) walk(n.children); } };
    if (Array.isArray(this.roots)) walk(this.roots);
  }

  attachListeners() {
    const api = (typeof chrome !== 'undefined' && chrome.bookmarks) ? chrome.bookmarks : browser.bookmarks;
    if (api.onCreated) api.onCreated.addListener(() => this.loadAll());
    if (api.onRemoved) api.onRemoved.addListener(() => this.loadAll());
    if (api.onChanged) api.onChanged.addListener(() => this.loadAll());
    if (api.onMoved) api.onMoved.addListener(() => this.loadAll());
    if (api.onImportEnded) api.onImportEnded.addListener(() => this.loadAll());
  }

  showError(err) {
    console.error(err);
    this.gridContainer.innerHTML = '';
    const warn = document.createElement('div');
    warn.className = 'bm-warning';
    warn.textContent = '讀取書籤時發生錯誤：' + (err && err.message ? err.message : String(err));
    this.gridContainer.appendChild(warn);
  }

  async addActiveTab() {
    try {
      const tabsAPI = (typeof chrome !== 'undefined' && chrome.tabs) ? chrome.tabs : (typeof browser !== 'undefined' ? browser.tabs : null);
      if (tabsAPI && tabsAPI.query) {
        if (tabsAPI === chrome.tabs) {
          tabsAPI.query({ active: true, currentWindow: true }, async (tabs) => {
            const t = tabs && tabs[0]; if (!t || !t.url) return; await this.addBookmark({ url: t.url, title: t.title || t.url });
          });
        } else {
          const tabs = await tabsAPI.query({ active: true, currentWindow: true });
          const t = tabs && tabs[0]; if (t && t.url) await this.addBookmark({ url: t.url, title: t.title || t.url });
        }
      } else {
        alert('無法取得目前分頁 (缺少 tabs 權限或瀏覽器不支援)。');
      }
    } catch (e) { this.showError(e); }
  }

  async addBookmark({ url, title, parentId = null }) {
    try {
      const targetParentId = parentId || this.currentFolderId || this.startFolderId || '1';
      const result = await this.api.create({ parentId: targetParentId, title, url });
      return result; // Return the created bookmark for further processing
    } catch (e) {
      this.showError(e);
      return null;
    }
  }

  async addFolder({ title, parentId = null }) {
    try {
      const targetParentId = parentId || this.currentFolderId || this.startFolderId || '1';
      await this.api.create({ parentId: targetParentId, title });
    } catch (e) { this.showError(e); }
  }

  findFolderByTitle(titles) {
    for (const node of this.flatIndex.values()) {
      if (!node.url && titles.includes(node.title)) return node;
    }
    return null;
  }

  findFolderByExactTitle(name) {
    if (!name) return null;
    const lower = name.toLowerCase();
    for (const node of this.flatIndex.values()) {
      if (!node.url && typeof node.title === 'string' && node.title.toLowerCase() === lower) return node;
    }
    return null;
  }

  getNodeById(id) { return id ? this.flatIndex.get(id) : null; }

  getTitleMaxLength() {
    const width = window.innerWidth;
    if (width <= 480) return 10;      // 手機：10個字符
    if (width <= 768) return 12;      // 平板：12個字符
    if (width <= 1200) return 15;     // 小桌面：15個字符
    return 18;                        // 大桌面：18個字符
  }

  getLongPressDelay() {
    return 300; // Fixed delay of 300ms
  }

  updateNavigation() {
    const folder = this.getNodeById(this.currentFolderId);
    if (folder) {
      this.navTitle.textContent = folder.title || '書籤';

      // Disable back button if:
      // 1. No parent folder, OR
      // 2. Current folder is the root folder (cannot go above root)
      const canGoBack = folder.parentId &&
                       this.currentFolderId !== this.rootFolderId;

      this.backBtn.disabled = !canGoBack;
    }
  }

  goBack() {
    const folder = this.getNodeById(this.currentFolderId);
    if (folder && folder.parentId) {
      // Check if we can go back (not above root folder)
      // Allow going back to root folder, but not beyond it
      const canGoBack = this.currentFolderId !== this.rootFolderId;

      if (canGoBack) {
        this.currentFolderId = folder.parentId;
        this.renderLevel();
        this.updateNavigation();
      }
    }
  }

  renderLevel() {
    const container = this.gridContainer;
    container.innerHTML = '';

    const folder = this.getNodeById(this.currentFolderId) || this.getNodeById(this.startFolderId);
    if (!folder) return;

    // Filter children by search
    const search = this.searchText;
    const children = (folder.children || []).filter(ch => {
      if (!search) return true;
      const text = (ch.title || '') + ' ' + (ch.url || '');
      return text.toLowerCase().includes(search);
    });

    // Sort children: apply custom order if available, otherwise default sort
    const customOrder = this.getCustomOrder(folder.id);
    let sortedChildren;

    console.log('Current folder:', folder.id, 'Custom order:', customOrder); // Debug log

    if (customOrder.length > 0) {
      // Apply custom order
      const orderedItems = [];
      const unorderedItems = [];

      // First, add items in custom order
      for (const id of customOrder) {
        const item = children.find(ch => ch.id === id);
        if (item) orderedItems.push(item);
      }

      // Then add any new items not in custom order
      for (const child of children) {
        if (!customOrder.includes(child.id)) {
          unorderedItems.push(child);
        }
      }

      // Sort unordered items by default rules
      unorderedItems.sort((a, b) => {
        if (!a.url && b.url) return -1;
        if (a.url && !b.url) return 1;
        const titleA = (a.title || '').toLowerCase();
        const titleB = (b.title || '').toLowerCase();
        return titleA.localeCompare(titleB);
      });

      sortedChildren = [...orderedItems, ...unorderedItems];
    } else {
      // Default sort: folders first, then bookmarks
      sortedChildren = children.sort((a, b) => {
        // Folders (no URL) come first
        if (!a.url && b.url) return -1;
        if (a.url && !b.url) return 1;

        // Within same type, sort alphabetically by title
        const titleA = (a.title || '').toLowerCase();
        const titleB = (b.title || '').toLowerCase();
        return titleA.localeCompare(titleB);
      });
    }

    // Render sorted children as bookmark items
    for (const child of sortedChildren) {
      const item = this.createBookmarkItem(child);
      container.appendChild(item);
    }

    this.updateNavigation();
  }

  createBookmarkItem(node) {
    const item = document.createElement('div');
    item.className = 'bookmark-item';
    item.dataset.id = node.id;
    item.draggable = false; // We'll handle dragging manually

    const icon = document.createElement('div');
    icon.className = 'bookmark-icon';

    if (node.url) {
      // Bookmark - check for custom icon first
      const customIcon = this.getCustomIcon(node.id);
      if (customIcon) {
        icon.style.backgroundImage = `url(${customIcon})`;
        icon.onerror = () => {
          // Fallback to favicon if custom icon fails
          const faviconUrl = `https://www.google.com/s2/favicons?sz=128&domain_url=${encodeURIComponent(node.url)}`;
          icon.style.backgroundImage = `url(${faviconUrl})`;
          icon.onerror = () => {
            icon.classList.add('default');
            icon.style.backgroundImage = '';
          };
        };
      } else {
        // Use favicon with higher resolution
        const faviconUrl = `https://www.google.com/s2/favicons?sz=128&domain_url=${encodeURIComponent(node.url)}`;
        icon.style.backgroundImage = `url(${faviconUrl})`;
        icon.onerror = () => {
          icon.classList.add('default');
          icon.style.backgroundImage = '';
        };
      }

      // Click handling is now done in drag events
    } else {
      // Folder
      icon.classList.add('folder');
      // Click handling is now done in drag events
    }

    const title = document.createElement('div');
    title.className = 'bookmark-title';

    // Limit title based on screen size
    let displayTitle = node.title || (node.url ? new URL(node.url).hostname : '未命名');
    let maxLength = this.getTitleMaxLength();

    if (displayTitle.length > maxLength) {
      displayTitle = displayTitle.substring(0, maxLength) + '...';
    }

    title.textContent = displayTitle;
    title.title = node.title || (node.url ? new URL(node.url).hostname : '未命名'); // Show full title on hover

    const menu = document.createElement('button');
    menu.className = 'bookmark-menu';
    menu.innerHTML = '⋯';
    menu.addEventListener('click', (e) => {
      e.stopPropagation();
      this.showContextMenu(e, node);
    });

    item.append(icon, title, menu);

    // Add long press and drag functionality
    this.addDragAndDropEvents(item, node);

    return item;
  }

  showContextMenu(event, node) {
    // Remove existing context menu
    const existing = document.querySelector('.context-menu');
    if (existing) existing.remove();

    const menu = document.createElement('div');
    menu.className = 'context-menu';
    menu.style.position = 'fixed';
    menu.style.left = event.clientX + 'px';
    menu.style.top = event.clientY + 'px';

    const items = [
      { text: this.getText('renameFolder'), action: () => this.showRenameModal(node) },
      { text: this.getText('moveItem'), action: () => this.showMoveModal(node) },
      { text: this.getText('deleteItem'), action: () => this.deleteNode(node), danger: true }
    ];

    if (node.url) {
      items.unshift({ text: this.getText('editBookmark'), action: () => this.showEditModal(node) });
    }

    items.forEach(itemData => {
      const menuItem = document.createElement('button');
      menuItem.className = 'context-menu-item';
      if (itemData.danger) menuItem.classList.add('danger');
      menuItem.textContent = itemData.text;
      menuItem.addEventListener('click', () => {
        menu.remove();
        itemData.action();
      });
      menu.appendChild(menuItem);
    });

    document.body.appendChild(menu);

    // Close menu when clicking outside
    const closeMenu = (e) => {
      if (!menu.contains(e.target)) {
        menu.remove();
        document.removeEventListener('click', closeMenu);
      }
    };
    setTimeout(() => document.addEventListener('click', closeMenu), 0);
  }

  async deleteNode(node) {
    if (confirm(`確定要刪除 "${node.title || '未命名'}" 嗎？`)) {
      try {
        if (node.url) {
          await this.api.remove(node.id);
        } else {
          await this.api.removeTree(node.id);
        }
      } catch (e) {
        this.showError(e);
      }
    }
  }
  // Modal functions
  showAddModal() {
    this.showAdvancedModal(this.getText('addBookmark'), {
      title: { label: this.getText('form.title'), value: '', placeholder: this.getText('form.titlePlaceholder') },
      url: { label: this.getText('form.url'), value: '', placeholder: this.getText('form.urlPlaceholder') },
      icon: { label: this.getText('form.icon'), value: '', placeholder: this.getText('form.iconPlaceholder') }
    }, async (data) => {
      if (data.url && data.title) {
        try {
          const targetParentId = this.currentFolderId || this.startFolderId || '1';

          // Create bookmark using Chrome Bookmarks API
          let bookmark = null;
          if (typeof chrome !== 'undefined' && chrome.bookmarks) {
            bookmark = await new Promise((resolve, reject) => {
              chrome.bookmarks.create({
                parentId: targetParentId,
                title: data.title,
                url: data.url
              }, (result) => {
                if (chrome.runtime.lastError) {
                  reject(new Error(chrome.runtime.lastError.message));
                } else {
                  resolve(result);
                }
              });
            });
          }

          // Save custom icon if provided
          if (data.icon.trim() && bookmark && bookmark.id) {
            this.setCustomIcon(bookmark.id, data.icon.trim());
          }

          // Refresh the current view
          this.renderLevel();
        } catch (e) {
          console.error('Error creating bookmark:', e);
          alert(this.getText('errors.addError') || '新增書籤時發生錯誤');
        }
      }
    });
  }

  showAddFolderModal() {
    this.showModal(this.getText('addFolder'), [
      { label: this.getText('form.folderName'), id: 'title', placeholder: this.getText('form.folderPlaceholder') }
    ], async (data) => {
      if (data.title) {
        await this.addFolder({ title: data.title });
      }
    });
  }

  showEditModal(node) {
    this.showAdvancedModal(this.getText('editBookmark'), {
      title: { label: this.getText('form.title'), value: node.title, placeholder: this.getText('form.titlePlaceholder') },
      url: { label: this.getText('form.url'), value: node.url, placeholder: this.getText('form.urlPlaceholder') },
      icon: { label: this.getText('form.icon'), value: this.getCustomIcon(node.id) || '', placeholder: this.getText('form.iconPlaceholder') }
    }, async (data) => {
      console.log('Edit modal callback triggered with data:', data);
      console.log('Original node:', node);

      try {
        const hasChanges = data.title !== node.title ||
                          data.url !== node.url ||
                          data.icon !== (this.getCustomIcon(node.id) || '');
        console.log('Has changes:', hasChanges);

        if (hasChanges) {
          console.log('Updating bookmark...');

          // Update bookmark using Chrome Bookmarks API
          if (typeof chrome !== 'undefined' && chrome.bookmarks) {
            console.log('Using Chrome Bookmarks API');
            await new Promise((resolve, reject) => {
              chrome.bookmarks.update(node.id, {
                title: data.title,
                url: data.url
              }, (result) => {
                if (chrome.runtime.lastError) {
                  console.error('Chrome API error:', chrome.runtime.lastError);
                  reject(new Error(chrome.runtime.lastError.message));
                } else {
                  console.log('Bookmark updated successfully:', result);
                  resolve(result);
                }
              });
            });
          } else {
            console.log('Chrome Bookmarks API not available');
          }

          // Save custom icon
          if (data.icon && data.icon.trim()) {
            console.log('Setting custom icon:', data.icon.trim());
            this.setCustomIcon(node.id, data.icon.trim());
          } else {
            console.log('Removing custom icon');
            this.removeCustomIcon(node.id);
          }

          console.log('Refreshing view...');
          this.renderLevel(); // Refresh to show new icon
          console.log('Edit completed successfully');
        } else {
          console.log('No changes detected, skipping update');
        }
      } catch (e) {
        console.error('Error updating bookmark:', e);
        alert(this.getText('errors.editError') || '更新書籤時發生錯誤');
      }
    });
  }

  showRenameModal(node) {
    this.showModal('重新命名', [
      { label: '名稱', id: 'title', placeholder: '輸入新名稱', value: node.title }
    ], async (data) => {
      if (data.title && data.title !== node.title) {
        try {
          await this.api.update(node.id, { title: data.title });
        } catch (e) { this.showError(e); }
      }
    });
  }

  async showMoveModal(node) {
    const overlay = document.createElement('div');
    overlay.className = 'move-modal-overlay';

    const modal = document.createElement('div');
    modal.className = 'move-modal';

    // Get all available folders for cross-folder movement
    const allFolders = await this.getAllAvailableFolders();

    modal.innerHTML = `
      <div class="move-modal-header">
        <h3 class="move-modal-title">${this.getText('moveItem')}</h3>
        <p class="move-modal-subtitle">${this.getText('move.moveItemDesc').replace('{item}', node.title)}</p>
      </div>
      <div class="move-modal-content">
        <input type="text" class="move-search" placeholder="${this.getText('move.searchFolders')}" id="move-search">
        <div class="move-folders-list" id="move-folders-list">
          ${this.renderFoldersList(allFolders, node)}
        </div>
      </div>
      <div class="move-modal-actions">
        <button class="move-modal-btn cancel" id="move-cancel">${this.getText('cancel')}</button>
        <button class="move-modal-btn confirm" id="move-confirm" disabled>${this.getText('move.moveHere')}</button>
      </div>
    `;

    overlay.appendChild(modal);
    document.body.appendChild(overlay);

    this.attachMoveModalEvents(overlay, node, allFolders);
  }

  async getAllAvailableFolders() {
    try {
      // Get the entire bookmark tree to find all folders
      const bookmarkTree = await new Promise((resolve) => {
        if (typeof chrome !== 'undefined' && chrome.bookmarks) {
          chrome.bookmarks.getTree(resolve);
        } else {
          resolve([]);
        }
      });

      const folders = [];
      const targetFolderNames = ['Tool', 'Learn', 'Job', 'Game', 'Video'];

      const findFolders = (nodes, path = '') => {
        for (const node of nodes) {
          if (node.children) {
            // Check if this is one of our target folders
            if (targetFolderNames.includes(node.title)) {
              folders.push({
                id: node.id,
                title: node.title,
                path: path ? `${path} > ${node.title}` : node.title,
                children: node.children
              });
            }
            // Also check subfolders within target folders
            else if (path && !node.url) {
              folders.push({
                id: node.id,
                title: node.title,
                path: `${path} > ${node.title}`,
                children: node.children
              });
            }

            // Continue searching in children
            const newPath = targetFolderNames.includes(node.title) ? node.title : path;
            findFolders(node.children, newPath);
          }
        }
      };

      findFolders(bookmarkTree);
      return folders;
    } catch (error) {
      console.error('Error getting folders:', error);
      return [];
    }
  }

  renderFoldersList(folders, currentNode) {
    return folders.map(folder => {
      const isCurrent = folder.id === currentNode.parentId;

      return `
        <div class="move-folder-item ${isCurrent ? 'current' : ''}" data-folder-id="${folder.id}">
          <div class="move-folder-icon">📁</div>
          <div class="move-folder-info">
            <div class="move-folder-name">${folder.title}</div>
            <div class="move-folder-path">${folder.path}</div>
          </div>
        </div>
      `;
    }).join('');
  }

  attachMoveModalEvents(overlay, node, allFolders) {
    const searchInput = overlay.querySelector('#move-search');
    const foldersList = overlay.querySelector('#move-folders-list');
    const cancelBtn = overlay.querySelector('#move-cancel');
    const confirmBtn = overlay.querySelector('#move-confirm');
    let selectedFolderId = null;

    // Search functionality
    searchInput.addEventListener('input', (e) => {
      const searchTerm = e.target.value.toLowerCase();
      const filteredFolders = allFolders.filter(folder =>
        folder.title.toLowerCase().includes(searchTerm) ||
        folder.path.toLowerCase().includes(searchTerm)
      );
      foldersList.innerHTML = this.renderFoldersList(filteredFolders, node);
      this.attachFolderClickEvents(foldersList, confirmBtn);
    });

    // Folder selection
    this.attachFolderClickEvents(foldersList, confirmBtn);

    // Cancel button
    cancelBtn.addEventListener('click', () => {
      overlay.remove();
    });

    // Confirm button
    confirmBtn.addEventListener('click', async () => {
      if (selectedFolderId) {
        try {
          await this.moveNodeToFolder(node, selectedFolderId);
          overlay.remove();
          this.renderLevel(); // Refresh the current view
        } catch (error) {
          console.error('Error moving item:', error);
          alert(this.getText('move.moveError'));
        }
      }
    });

    // Close on overlay click
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        overlay.remove();
      }
    });

    // Store selected folder ID for confirm button
    overlay.addEventListener('click', (e) => {
      const folderItem = e.target.closest('.move-folder-item');
      if (folderItem && !folderItem.classList.contains('current')) {
        selectedFolderId = folderItem.dataset.folderId;
      }
    });
  }

  attachFolderClickEvents(foldersList, confirmBtn) {
    const folderItems = foldersList.querySelectorAll('.move-folder-item');

    folderItems.forEach(item => {
      item.addEventListener('click', () => {
        if (item.classList.contains('current')) return;

        // Remove previous selection
        folderItems.forEach(i => i.classList.remove('selected'));

        // Add selection to clicked item
        item.classList.add('selected');

        // Enable confirm button
        confirmBtn.disabled = false;
      });
    });
  }

  async moveNodeToFolder(node, targetFolderId) {
    try {
      if (typeof chrome !== 'undefined' && chrome.bookmarks) {
        await new Promise((resolve, reject) => {
          chrome.bookmarks.move(node.id, { parentId: targetFolderId }, (result) => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              resolve(result);
            }
          });
        });
      }
    } catch (error) {
      throw error;
    }
  }

  // Check if a folder is within the root folder scope
  isWithinRootFolder(folderId) {
    if (!this.rootFolderId) return true; // No restriction

    let current = this.getNodeById(folderId);
    while (current) {
      if (current.id === this.rootFolderId) return true;
      current = this.getNodeById(current.parentId);
    }
    return false;
  }

  showModal(title, fields, onSubmit) {
    const overlay = document.createElement('div');
    overlay.className = 'modal-overlay';

    const modal = document.createElement('div');
    modal.className = 'modal';

    const modalTitle = document.createElement('div');
    modalTitle.className = 'modal-title';
    modalTitle.textContent = title;

    const form = document.createElement('form');
    const inputs = {};

    fields.forEach(field => {
      const input = document.createElement('input');
      input.className = 'modal-input';
      input.type = field.type || 'text';
      input.placeholder = field.placeholder || '';
      input.value = field.value || '';
      input.id = field.id;
      inputs[field.id] = input;
      form.appendChild(input);
    });

    const buttons = document.createElement('div');
    buttons.className = 'modal-buttons';

    const cancelBtn = document.createElement('button');
    cancelBtn.className = 'modal-btn secondary';
    cancelBtn.type = 'button';
    cancelBtn.textContent = this.getText('cancel');
    cancelBtn.addEventListener('click', () => overlay.remove());

    const submitBtn = document.createElement('button');
    submitBtn.className = 'modal-btn primary';
    submitBtn.type = 'submit';
    submitBtn.textContent = this.getText('confirm');

    form.addEventListener('submit', (e) => {
      e.preventDefault();
      const data = {};
      Object.keys(inputs).forEach(key => {
        data[key] = inputs[key].value.trim();
      });
      overlay.remove();
      onSubmit(data);
    });

    buttons.append(cancelBtn, submitBtn);
    modal.append(modalTitle, form, buttons);
    overlay.appendChild(modal);
    document.body.appendChild(overlay);

    // Focus first input
    if (fields.length > 0) {
      inputs[fields[0].id].focus();
    }

    // Close on overlay click
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) overlay.remove();
    });
  }

  // Custom icon management
  loadCustomIcons() {
    try {
      const stored = localStorage.getItem('bookmark-custom-icons');
      return stored ? JSON.parse(stored) : {};
    } catch (e) {
      return {};
    }
  }

  saveCustomIcons() {
    try {
      localStorage.setItem('bookmark-custom-icons', JSON.stringify(this.customIcons));
    } catch (e) {
      console.error('Failed to save custom icons:', e);
    }
  }

  getCustomIcon(bookmarkId) {
    return this.customIcons[bookmarkId];
  }

  setCustomIcon(bookmarkId, iconUrl) {
    this.customIcons[bookmarkId] = iconUrl;
    this.saveCustomIcons();
  }

  removeCustomIcon(bookmarkId) {
    delete this.customIcons[bookmarkId];
    this.saveCustomIcons();
  }

  // Custom order management
  loadCustomOrder() {
    try {
      const stored = localStorage.getItem('bookmark-custom-order');
      return stored ? JSON.parse(stored) : {};
    } catch (e) {
      return {};
    }
  }

  saveCustomOrder() {
    try {
      localStorage.setItem('bookmark-custom-order', JSON.stringify(this.customOrder));
    } catch (e) {
      console.error('Failed to save custom order:', e);
    }
  }

  getCustomOrder(folderId) {
    return this.customOrder[folderId] || [];
  }

  setCustomOrder(folderId, order) {
    console.log('Setting custom order for folder', folderId, ':', order); // Debug log
    this.customOrder[folderId] = order;
    this.saveCustomOrder();
    console.log('Custom order saved:', this.customOrder); // Debug log
  }

  // Drag and drop functionality
  addDragAndDropEvents(item, node) {
    let startX, startY, startTime;
    let longPressTriggered = false;

    // Mouse events
    item.addEventListener('mousedown', (e) => {
      if (e.target.closest('.bookmark-menu')) return; // Don't drag when clicking menu

      startX = e.clientX;
      startY = e.clientY;
      startTime = Date.now();
      longPressTriggered = false;

      this.longPressTimer = setTimeout(() => {
        longPressTriggered = true;
        item.classList.add('long-press-active');
        setTimeout(() => {
          item.classList.remove('long-press-active');
          this.startDrag(item, node, e);
        }, 100); // Brief visual feedback before starting drag
      }, this.getLongPressDelay()); // Use setting value
    });

    item.addEventListener('mousemove', (e) => {
      if (this.longPressTimer && !longPressTriggered) {
        const deltaX = Math.abs(e.clientX - startX);
        const deltaY = Math.abs(e.clientY - startY);

        // Cancel long press if moved too much
        if (deltaX > 10 || deltaY > 10) {
          clearTimeout(this.longPressTimer);
          this.longPressTimer = null;
        }
      }

      if (this.isDragging && this.dragElement === item) {
        this.handleDragMove(e);
      }
    });

    item.addEventListener('mouseup', (e) => {
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }

      if (this.isDragging && this.dragElement === item) {
        this.endDrag(e);
      } else if (!longPressTriggered && Date.now() - startTime < this.getLongPressDelay()) {
        // Normal click
        if (!e.target.closest('.bookmark-menu')) {
          if (node.url) {
            window.open(node.url, '_blank');
          } else {
            this.currentFolderId = node.id;
            this.renderLevel();
            this.updateNavigation();
          }
        }
      }
    });

    // Touch events for mobile
    item.addEventListener('touchstart', (e) => {
      if (e.target.closest('.bookmark-menu')) return;

      const touch = e.touches[0];
      startX = touch.clientX;
      startY = touch.clientY;
      startTime = Date.now();
      longPressTriggered = false;

      this.longPressTimer = setTimeout(() => {
        longPressTriggered = true;
        item.classList.add('long-press-active');
        setTimeout(() => {
          item.classList.remove('long-press-active');
          this.startDrag(item, node, touch);
        }, 100);
      }, this.getLongPressDelay()); // Use setting value
    });

    item.addEventListener('touchmove', (e) => {
      if (this.longPressTimer && !longPressTriggered) {
        const touch = e.touches[0];
        const deltaX = Math.abs(touch.clientX - startX);
        const deltaY = Math.abs(touch.clientY - startY);

        if (deltaX > 10 || deltaY > 10) {
          clearTimeout(this.longPressTimer);
          this.longPressTimer = null;
        }
      }

      if (this.isDragging && this.dragElement === item) {
        e.preventDefault();
        this.handleDragMove(e.touches[0]);
      }
    });

    item.addEventListener('touchend', (e) => {
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }

      if (this.isDragging && this.dragElement === item) {
        this.endDrag(e.changedTouches[0]);
      } else if (!longPressTriggered && Date.now() - startTime < this.getLongPressDelay()) {
        if (!e.target.closest('.bookmark-menu')) {
          if (node.url) {
            window.open(node.url, '_blank');
          } else {
            this.currentFolderId = node.id;
            this.renderLevel();
            this.updateNavigation();
          }
        }
      }
    });
  }

  startDrag(item, _node, pointer) {
    this.isDragging = true;
    this.dragElement = item;

    // Add dragging class for visual feedback
    item.classList.add('dragging');
    document.body.classList.add('dragging-active');

    // Create placeholder
    this.dragPlaceholder = document.createElement('div');
    this.dragPlaceholder.className = 'drag-placeholder bookmark-item'; // Add bookmark-item class for proper filtering
    this.dragPlaceholder.style.width = item.offsetWidth + 'px';
    this.dragPlaceholder.style.height = item.offsetHeight + 'px';
    this.dragPlaceholder.dataset.placeholder = 'true'; // Mark as placeholder

    // Insert placeholder after the dragged item
    item.parentNode.insertBefore(this.dragPlaceholder, item.nextSibling);

    // Make item follow cursor
    item.style.position = 'fixed';
    item.style.zIndex = '1000';
    item.style.pointerEvents = 'none';
    item.style.transform = 'scale(1.05)';

    this.updateDragPosition(item, pointer);

    // Add global mouse/touch move and up listeners
    document.addEventListener('mousemove', this.handleGlobalDragMove);
    document.addEventListener('mouseup', this.handleGlobalDragEnd);
    document.addEventListener('touchmove', this.handleGlobalDragMove);
    document.addEventListener('touchend', this.handleGlobalDragEnd);
  }

  handleDragMove(pointer) {
    if (!this.isDragging || !this.dragElement) return;

    this.updateDragPosition(this.dragElement, pointer);

    // Find drop target - look for bookmark items that aren't being dragged or placeholders
    const elementBelow = document.elementFromPoint(pointer.clientX, pointer.clientY);
    const dropTarget = elementBelow?.closest('.bookmark-item:not(.dragging)');

    if (dropTarget && this.dragPlaceholder && !dropTarget.dataset.placeholder) {
      const container = this.gridContainer;
      const rect = dropTarget.getBoundingClientRect();

      // Calculate if we're in the top or bottom half of the item
      const relativeY = pointer.clientY - rect.top;
      const itemHeight = rect.height;

      if (relativeY < itemHeight / 2) {
        // Insert before the target
        if (dropTarget.previousElementSibling !== this.dragPlaceholder) {
          container.insertBefore(this.dragPlaceholder, dropTarget);
        }
      } else {
        // Insert after the target
        const nextSibling = dropTarget.nextElementSibling;
        if (nextSibling !== this.dragPlaceholder) {
          if (nextSibling) {
            container.insertBefore(this.dragPlaceholder, nextSibling);
          } else {
            container.appendChild(this.dragPlaceholder);
          }
        }
      }
    }
  }

  updateDragPosition(element, pointer) {
    const rect = element.getBoundingClientRect();
    element.style.left = (pointer.clientX - rect.width / 2) + 'px';
    element.style.top = (pointer.clientY - rect.height / 2) + 'px';
  }

  endDrag(_pointer) {
    if (!this.isDragging || !this.dragElement) return;

    const container = this.gridContainer;

    // Reset dragged element styles first
    this.dragElement.style.position = '';
    this.dragElement.style.zIndex = '';
    this.dragElement.style.pointerEvents = '';
    this.dragElement.style.transform = '';
    this.dragElement.style.left = '';
    this.dragElement.style.top = '';
    this.dragElement.classList.remove('dragging');

    // Insert element at placeholder position
    if (this.dragPlaceholder && this.dragPlaceholder.parentNode) {
      this.dragPlaceholder.parentNode.insertBefore(this.dragElement, this.dragPlaceholder);
    }

    // Cleanup placeholder
    if (this.dragPlaceholder) {
      this.dragPlaceholder.remove();
      this.dragPlaceholder = null;
    }

    // Get final order and save it
    const finalOrder = Array.from(container.children)
      .filter(child => child.classList.contains('bookmark-item'))
      .map(child => child.dataset.id)
      .filter(id => id); // Remove any undefined IDs

    console.log('Saving new order:', finalOrder); // Debug log
    this.setCustomOrder(this.currentFolderId, finalOrder);

    // Cleanup drag state
    document.body.classList.remove('dragging-active');
    this.isDragging = false;
    this.dragElement = null;

    // Remove global listeners
    document.removeEventListener('mousemove', this.handleGlobalDragMove);
    document.removeEventListener('mouseup', this.handleGlobalDragEnd);
    document.removeEventListener('touchmove', this.handleGlobalDragMove);
    document.removeEventListener('touchend', this.handleGlobalDragEnd);

    // Force re-render to ensure order is applied
    setTimeout(() => {
      this.renderLevel();
    }, 100);
  }

  // Bound methods for global event listeners
  handleGlobalDragMove = (e) => {
    if (this.isDragging) {
      const pointer = e.touches ? e.touches[0] : e;
      this.handleDragMove(pointer);
    }
  }

  handleGlobalDragEnd = (e) => {
    if (this.isDragging) {
      const pointer = e.changedTouches ? e.changedTouches[0] : e;
      this.endDrag(pointer);
    }
  }

  // Advanced modal with icon preview
  showAdvancedModal(title, fields, onSubmit) {
    const overlay = document.createElement('div');
    overlay.className = 'modal-overlay';

    const modal = document.createElement('div');
    modal.className = 'modal advanced-modal';

    const modalTitle = document.createElement('div');
    modalTitle.className = 'modal-title';
    modalTitle.textContent = title;

    const form = document.createElement('form');
    const inputs = {};

    // Create input fields
    Object.keys(fields).forEach(key => {
      const field = fields[key];
      const fieldContainer = document.createElement('div');
      fieldContainer.className = 'modal-field';

      const label = document.createElement('label');
      label.textContent = field.label;
      label.className = 'modal-label';

      const input = document.createElement('input');
      input.className = 'modal-input';
      input.type = field.type || 'text';
      input.placeholder = field.placeholder || '';
      input.value = field.value || '';
      input.id = key;
      inputs[key] = input;

      fieldContainer.append(label, input);

      // Add icon preview for icon field
      if (key === 'icon') {
        const preview = document.createElement('div');
        preview.className = 'icon-preview';

        const previewImg = document.createElement('img');
        previewImg.className = 'icon-preview-img';
        previewImg.style.width = '32px';
        previewImg.style.height = '32px';
        previewImg.style.borderRadius = '6px';
        previewImg.style.marginTop = '8px';
        previewImg.style.display = 'none';

        const previewText = document.createElement('div');
        previewText.className = 'icon-preview-text';
        previewText.textContent = '圖標預覽將在此顯示';
        previewText.style.fontSize = '12px';
        previewText.style.color = '#666';
        previewText.style.marginTop = '8px';

        preview.append(previewImg, previewText);
        fieldContainer.appendChild(preview);

        // Update preview when URL changes
        input.addEventListener('input', () => {
          const url = input.value.trim();
          if (url) {
            previewImg.src = url;
            previewImg.style.display = 'block';
            previewText.style.display = 'none';
            previewImg.onerror = () => {
              previewImg.style.display = 'none';
              previewText.style.display = 'block';
              previewText.textContent = '無法載入圖標';
            };
          } else {
            previewImg.style.display = 'none';
            previewText.style.display = 'block';
            previewText.textContent = '圖標預覽將在此顯示';
          }
        });

        // Initialize preview
        if (field.value) {
          input.dispatchEvent(new Event('input'));
        }
      }

      form.appendChild(fieldContainer);
    });

    const buttons = document.createElement('div');
    buttons.className = 'modal-buttons';

    const cancelBtn = document.createElement('button');
    cancelBtn.className = 'modal-btn secondary';
    cancelBtn.type = 'button';
    cancelBtn.textContent = this.getText('cancel');
    cancelBtn.addEventListener('click', () => overlay.remove());

    const submitBtn = document.createElement('button');
    submitBtn.className = 'modal-btn primary';
    submitBtn.type = 'submit';
    submitBtn.textContent = this.getText('confirm');

    // Add direct click handler as backup
    submitBtn.addEventListener('click', (e) => {
      console.log('Submit button clicked directly');
      e.preventDefault();
      const data = {};
      Object.keys(inputs).forEach(key => {
        data[key] = inputs[key].value.trim();
      });
      console.log('Form data collected via click:', data);
      overlay.remove();
      console.log('Calling onSubmit callback via click...');
      onSubmit(data);
    });

    // Add buttons to form instead of separate container
    buttons.append(cancelBtn, submitBtn);
    form.appendChild(buttons);

    form.addEventListener('submit', (e) => {
      console.log('Form submit event triggered');
      e.preventDefault();
      const data = {};
      Object.keys(inputs).forEach(key => {
        data[key] = inputs[key].value.trim();
      });
      console.log('Form data collected:', data);
      overlay.remove();
      console.log('Calling onSubmit callback...');
      onSubmit(data);
    });

    modal.append(modalTitle, form);
    overlay.appendChild(modal);
    document.body.appendChild(overlay);

    // Focus first input
    const firstInput = Object.values(inputs)[0];
    if (firstInput) firstInput.focus();

    // Close on overlay click
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) overlay.remove();
    });
  }

  // Cleanup method
  destroy() {
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler);
    }
  }

  getCategory(node) {
    if (!node.url) return '資料夾';
    const u = node.url.toLowerCase();
    if (u.includes('youtube.com')) return '影音';
    if (u.includes('github.com') || u.includes('gitlab.com')) return '程式/開發';
    if (u.includes('google.com') || u.includes('gmail.com') || u.includes('drive.google.com')) return 'Google';
    if (u.includes('news') || u.includes('medium.com') || u.includes('blog')) return '閱讀';
    if (u.includes('fju.edu.tw')) return '學校';
    return '其他';
  }

  renderTree() {
    const container = this.treeContainer; container.innerHTML = ''; if (!this.roots) return;
    const search = this.searchText;
    const renderNode = (node, depth = 0) => {
      const isInvisibleRoot = !node.title && !node.url && node.children && depth === 0;
      if (search && !isInvisibleRoot) {
        const text = (node.title || '') + ' ' + (node.url || '');
        if (!text.toLowerCase().includes(search)) {
          if (!node.children || !node.children.some(child => ((child.title || '') + ' ' + (child.url || '')).toLowerCase().includes(search))) return null;
        }
      }
      const el = document.createElement('div'); el.className = 'bm-node'; el.style.marginLeft = Math.max(0, (isInvisibleRoot ? 0 : depth) * 14) + 'px';
      if (!node.url) {
        if (isInvisibleRoot) {
          const wrap = document.createElement('div');
          if (node.children) for (const c of node.children) { const childEl = renderNode(c, depth); if (childEl) wrap.appendChild(childEl); }
          el.appendChild(wrap); return el;
        }
        const row = document.createElement('div'); row.className = 'bm-folder';
        const toggle = document.createElement('span'); toggle.className = 'bm-folder-toggle'; toggle.textContent = '▾'; let collapsed = false;
        const name = document.createElement('span'); name.className = 'bm-folder-name'; name.textContent = node.title || this.getText('messages.unnamedFolder');
        const spacer = document.createElement('div'); spacer.className = 'bm-spacer';
        const actions = document.createElement('div'); actions.className = 'bm-actions';
        const del = document.createElement('button'); del.className = 'bm-action-btn'; del.title = this.getText('messages.deleteFolderTitle'); del.textContent = '🗑️'; del.addEventListener('click', () => this.deleteNode(node));
        row.append(toggle, name, spacer, actions); actions.append(del); el.appendChild(row);
        const wrap = document.createElement('div');
        toggle.addEventListener('click', () => { collapsed = !collapsed; toggle.textContent = collapsed ? '▸' : '▾'; wrap.style.display = collapsed ? 'none' : ''; });
        if (node.children) for (const c of node.children) { const childEl = renderNode(c, depth + 1); if (childEl) wrap.appendChild(childEl); }
        el.appendChild(wrap);
      } else {
        const row = document.createElement('div'); row.className = 'bm-bookmark';
        const fav = document.createElement('img'); fav.className = 'bm-favicon'; fav.referrerPolicy = 'no-referrer'; fav.src = `https://www.google.com/s2/favicons?sz=32&domain_url=${encodeURIComponent(node.url)}`;
        const t = document.createElement('span'); t.className = 'bm-title'; t.textContent = node.title || '(未命名)';
        const u = document.createElement('span'); u.className = 'bm-url'; u.textContent = ` - ${node.url}`;
        const cat = document.createElement('span'); cat.className = 'bm-url'; cat.textContent = ` [${this.getCategory(node)}]`;
        const spacer = document.createElement('div'); spacer.className = 'bm-spacer';
        const actions = document.createElement('div'); actions.className = 'bm-actions';
        const open = document.createElement('button'); open.className = 'bm-action-btn'; open.title = this.getText('open'); open.textContent = '↗'; open.addEventListener('click', () => this.openBookmark(node));
        const del = document.createElement('button'); del.className = 'bm-action-btn'; del.title = this.getText('messages.deleteBookmarkTitle'); del.textContent = '🗑️'; del.addEventListener('click', () => this.deleteNode(node));
        row.append(fav, t, u, cat, spacer, actions); actions.append(open, del); el.appendChild(row);
      }
      return el;
    };
    // If a folder filter is set, show only that folder and its descendants
    if (this.folderFilter) {
      const folder = this.findFolderByExactTitle(this.folderFilter);
      if (folder) {
        const el = renderNode(folder, 0);
        if (el) container.appendChild(el);
        return;
      }
    }
    for (const root of this.roots) { const el = renderNode(root, 0); if (el) container.appendChild(el); }
  }

  // Refresh method for language changes
  refresh() {
    // Re-render the current level to update language
    this.renderLevel();

    // Update navigation title
    const navTitle = document.getElementById('nav-title');
    if (navTitle) {
      navTitle.textContent = this.getText('title');
    }

    // Update search placeholder
    const searchInput = document.getElementById('bm-search');
    if (searchInput) {
      searchInput.placeholder = this.getText('search');
    }

    // Update action buttons
    const addBtn = document.querySelector('.bm-btn:not(.secondary)');
    if (addBtn) {
      addBtn.textContent = this.getText('addBookmark');
    }

    const addFolderBtn = document.querySelector('.bm-btn.secondary');
    if (addFolderBtn) {
      addFolderBtn.textContent = this.getText('addFolder');
    }
  }

  // Get text based on current language
  getText(key) {
    if (window.Lang) {
      return window.Lang.t(`bookmarks.${key}`);
    }

    // Fallback values
    const fallbacks = {
      title: this.folderFilter || '書籤',
      search: '搜尋書籤...',
      addBookmark: '新增書籤',
      addFolder: '新增資料夾',
      editBookmark: '編輯書籤',
      renameFolder: '重新命名',
      moveItem: '移動',
      deleteItem: '刪除',
      cancel: '取消',
      confirm: '確定',
      save: '儲存',
      open: '開啟',
      'form.title': '標題',
      'form.url': '網址',
      'form.icon': '自訂圖標網址',
      'form.folderName': '資料夾名稱',
      'form.titlePlaceholder': '輸入書籤標題',
      'form.urlPlaceholder': 'https://example.com',
      'form.iconPlaceholder': 'https://example.com/icon.png (選填)',
      'form.folderPlaceholder': '輸入資料夾名稱',
      'messages.unnamedFolder': '(未命名資料夾)',
      'messages.deleteBookmarkTitle': '刪除書籤',
      'messages.deleteFolderTitle': '刪除資料夾',
      'move.moveItemDesc': '選擇要將 "{item}" 移動到的資料夾',
      'move.searchFolders': '搜尋資料夾...',
      'move.moveHere': '移動到這裡',
      'move.moveError': '移動失敗，請稍後再試',
      'errors.addError': '新增書籤時發生錯誤',
      'errors.editError': '更新書籤時發生錯誤'
    };

    return fallbacks[key] || key;
  }
}