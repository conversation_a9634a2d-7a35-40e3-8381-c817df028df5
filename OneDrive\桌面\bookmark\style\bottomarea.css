#toolbar {
    height: 120px;
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

.tool {
    width: 85px;
    height: 85px;
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    background-repeat: no-repeat;
    background-size: 100% 100%;
     transform-origin: bottom;
}

.tool:hover {
    transform: scale(1.65);
}

#learnbtn {
    background-image: url(../utils/learn.png);
}

#jobbtn {
    background-image: url(../utils/job.png)
}

#gamebtn{
     background-image: url(../utils/game.png)
}

#videobtn{
    background-image: url(../utils/video.png)
}

#googlebtn{
    background-image: url(../utils/google.png)
}

#fjubtn{
    background-image: url(../utils/fju.png)
}

#homebtn{
   background-image: url(../utils/home.png)
}

#toolbtn{
    background-image: url(../utils/tool.png)
}

/* Responsive Design for Toolbar */
@media (max-width: 1024px) {
    #toolbar {
        width: 800px;
        height: 110px;
        gap: 25px;
    }

    .tool {
        width: 75px;
        height: 75px;
    }
}

@media (max-width: 768px) {
    #toolbar {
        width: 95%;
        max-width: 700px;
        height: 100px;
        gap: 20px;
        padding: 0 10px;
    }

    .tool {
        width: 65px;
        height: 65px;
        border-radius: 15px;
    }

    .tool:hover {
        transform: scale(1.4);
    }
}

@media (max-width: 640px) {
    #toolbar {
        width: 95%;
        max-width: 600px;
        height: 90px;
        gap: 15px;
        padding: 0 8px;
    }

    .tool {
        width: 55px;
        height: 55px;
        border-radius: 12px;
    }

    .tool:hover {
        transform: scale(1.3);
    }
}

@media (max-width: 480px) {
    #toolbar {
        width: 98%;
        height: 80px;
        gap: 10px;
        padding: 0 5px;
        border-radius: 12px;
    }

    .tool {
        width: 45px;
        height: 45px;
        border-radius: 10px;
    }

    .tool:hover {
        transform: scale(1.2);
    }
}

@media (max-width: 360px) {
    #toolbar {
        width: 100%;
        height: 70px;
        gap: 8px;
        padding: 0 3px;
        border-radius: 10px;
    }

    .tool {
        width: 38px;
        height: 38px;
        border-radius: 8px;
    }

    .tool:hover {
        transform: scale(1.15);
    }
}

@media (max-width: 320px) {
    #toolbar {
        height: 65px;
        gap: 5px;
    }

    .tool {
        width: 35px;
        height: 35px;
        border-radius: 7px;
    }

    .tool:hover {
        transform: scale(1.1);
    }
}