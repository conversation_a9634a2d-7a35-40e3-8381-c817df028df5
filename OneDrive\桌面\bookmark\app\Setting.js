class Setting {
    constructor() {
        this.settings = this.loadSettings();
        this.defaultSettings = {
            language: 'zh',
            username: '我',
            defaultPage: 'Home'
        };
    }

    async AppendElement() {
        await this.createSettingsPage();
    }

    loadSettings() {
        try {
            const stored = localStorage.getItem('bookmark-settings');
            return stored ? { ...this.defaultSettings, ...JSON.parse(stored) } : { ...this.defaultSettings };
        } catch (e) {
            console.error('Failed to load settings:', e);
            return { ...this.defaultSettings };
        }
    }

    saveSettings() {
        try {
            localStorage.setItem('bookmark-settings', JSON.stringify(this.settings));
            this.applySettings();
        } catch (e) {
            console.error('Failed to save settings:', e);
        }
    }

    getSetting(key) {
        return this.settings[key] !== undefined ? this.settings[key] : this.defaultSettings[key];
    }

    setSetting(key, value) {
        this.settings[key] = value;
        this.saveSettings();

        // If username changed and we're not on settings page, refresh home page
        if (key === 'username' && window.Pages && window.Pages.Home) {
            const currentPage = document.getElementById('homepage');
            if (currentPage) {
                window.Pages.Home.refresh();
            }
        }
    }

    async applySettings() {
        // Apply language
        const language = this.getSetting('language');
        if (window.Lang && language) {
            await window.Lang.setLanguage(language);
            window.Lang.updatePageTexts();

            // Refresh current page if it's displayed
            if (window.Pages) {
                const currentPage = document.getElementById('homepage');
                const googlePage = document.getElementById('googlecontainer');
                const fjuPage = document.getElementById('fjucontainer');
                const fjuBackBtn = document.getElementById('backbtn');
                const learnPage = document.querySelector('.learn-container');

                if (currentPage && window.Pages.Home) {
                    window.Pages.Home.refresh();
                } else if (googlePage && window.Pages.Google) {
                    window.Pages.Google.refresh();
                } else if ((fjuPage || fjuBackBtn) && window.Pages.FJU) {
                    window.Pages.FJU.refresh();
                } else if (learnPage) {
                    // For Learn-based pages (Learn, Game, Video, Job, Tool), refresh the current page
                    const currentPageType = learnPage.getAttribute('data-page-type');
                    if (currentPageType && window.Pages[currentPageType] && window.Pages[currentPageType].refresh) {
                        window.Pages[currentPageType].refresh();
                    }
                }
            }
        }
    }

    async createSettingsPage() {
        // Clear any existing content first
        Dom.TopContent.innerHTML = "";

        const container = document.createElement('div');
        container.id = 'settings-container';

        // Header
        const header = document.createElement('div');
        header.id = 'settings-header';

        const title = window.Lang.createElement('h1', 'settings.title', 'settings-title');
        const subtitle = window.Lang.createElement('p', 'settings.subtitle', 'settings-subtitle');

        header.append(title, subtitle);

        // Settings content
        const content = document.createElement('div');
        content.id = 'settings-content';

        // Create setting sections
        content.appendChild(this.createGeneralSection());
        content.appendChild(this.createDataSection());

        container.append(header, content);
        Dom.TopContent.appendChild(container);

        // Apply current settings
        await this.applySettings();
    }

    createGeneralSection() {
        const section = this.createSection('settings.general.title', 'general-settings');

        // Username setting
        section.appendChild(this.createTextInput(
            'username',
            'settings.general.username',
            'settings.general.usernameDesc',
            this.getSetting('username')
        ));

        // Default page setting
        const pageOptions = [
            { value: 'Home', labelKey: 'settings.options.pages.Home' },
            { value: 'Learn', labelKey: 'settings.options.pages.Learn' },
            { value: 'Game', labelKey: 'settings.options.pages.Game' },
            { value: 'Video', labelKey: 'settings.options.pages.Video' },
            { value: 'Job', labelKey: 'settings.options.pages.Job' }
        ].map(opt => ({ value: opt.value, label: window.Lang.t(opt.labelKey) }));

        section.appendChild(this.createSelect(
            'defaultPage',
            'settings.general.defaultPage',
            'settings.general.defaultPageDesc',
            pageOptions,
            this.getSetting('defaultPage')
        ));

        // Language setting
        const langOptions = [
            { value: 'zh', labelKey: 'settings.options.languages.zh' },
            { value: 'en', labelKey: 'settings.options.languages.en' },
            { value: 'ja', labelKey: 'settings.options.languages.ja' }
        ].map(opt => ({ value: opt.value, label: window.Lang.t(opt.labelKey) }));

        section.appendChild(this.createSelect(
            'language',
            'settings.general.language',
            'settings.general.languageDesc',
            langOptions,
            this.getSetting('language'),
            true // isLanguageSelect
        ));

        return section;
    }


    createDataSection() {
        const section = this.createSection('settings.data.title', 'data-settings');

        // Export settings
        const exportBtn = this.createButton(
            'settings.data.exportSettings',
            'settings.data.exportSettingsDesc',
            () => this.exportSettings()
        );
        section.appendChild(exportBtn);

        // Import settings
        const importBtn = this.createButton(
            'settings.data.importSettings',
            'settings.data.importSettingsDesc',
            () => this.importSettings()
        );
        section.appendChild(importBtn);

        // Reset settings
        const resetBtn = this.createButton(
            'settings.data.resetSettings',
            'settings.data.resetSettingsDesc',
            () => this.resetSettings(),
            'danger'
        );
        section.appendChild(resetBtn);

        // Clear all data
        const clearBtn = this.createButton(
            'settings.data.clearAllData',
            'settings.data.clearAllDataDesc',
            () => this.clearAllData(),
            'danger'
        );
        section.appendChild(clearBtn);

        return section;
    }

    // Helper methods for creating UI elements
    createSection(titleKey, id) {
        const section = document.createElement('div');
        section.className = 'settings-section';
        section.id = id;

        const header = window.Lang.createElement('h2', titleKey, 'section-title');
        section.appendChild(header);
        return section;
    }

    createTextInput(key, labelKey, descKey, value) {
        const container = document.createElement('div');
        container.className = 'setting-item';

        const labelEl = window.Lang.createElement('label', labelKey, 'setting-label');
        const desc = window.Lang.createElement('p', descKey, 'setting-description');

        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'setting-input';
        input.value = value;
        input.addEventListener('change', () => {
            this.setSetting(key, input.value);
        });

        container.append(labelEl, desc, input);
        return container;
    }

    createSelect(key, labelKey, descKey, options, value, isLanguageSelect = false) {
        const container = document.createElement('div');
        container.className = 'setting-item';

        const labelEl = window.Lang.createElement('label', labelKey, 'setting-label');
        const desc = window.Lang.createElement('p', descKey, 'setting-description');

        const select = document.createElement('select');
        select.className = 'setting-select';

        options.forEach(option => {
            const optionEl = document.createElement('option');
            optionEl.value = option.value;
            optionEl.textContent = option.label;
            optionEl.selected = option.value === value;
            select.appendChild(optionEl);
        });

        select.addEventListener('change', async () => {
            this.setSetting(key, select.value);

            // If this is language select, update the page immediately
            if (isLanguageSelect) {
                await this.switchLanguage(select.value);
            }
        });

        container.append(labelEl, desc, select);
        return container;
    }

    createToggle(key, labelKey, descKey, value) {
        const container = document.createElement('div');
        container.className = 'setting-item';

        const labelEl = window.Lang.createElement('label', labelKey, 'setting-label');
        const desc = window.Lang.createElement('p', descKey, 'setting-description');

        const toggleContainer = document.createElement('div');
        toggleContainer.className = 'toggle-container';

        const toggle = document.createElement('input');
        toggle.type = 'checkbox';
        toggle.className = 'setting-toggle';
        toggle.checked = value;
        toggle.addEventListener('change', () => {
            this.setSetting(key, toggle.checked);
        });

        const slider = document.createElement('span');
        slider.className = 'toggle-slider';

        toggleContainer.append(toggle, slider);
        container.append(labelEl, desc, toggleContainer);
        return container;
    }

    createRange(key, labelKey, descKey, min, max, step, value, formatter) {
        const container = document.createElement('div');
        container.className = 'setting-item';

        const labelEl = window.Lang.createElement('label', labelKey, 'setting-label');
        const desc = window.Lang.createElement('p', descKey, 'setting-description');

        const rangeContainer = document.createElement('div');
        rangeContainer.className = 'range-container';

        const range = document.createElement('input');
        range.type = 'range';
        range.className = 'setting-range';
        range.min = min;
        range.max = max;
        range.step = step;
        range.value = value;

        const valueDisplay = document.createElement('span');
        valueDisplay.className = 'range-value';
        valueDisplay.textContent = formatter ? formatter(value) : value;

        range.addEventListener('input', () => {
            const newValue = parseInt(range.value);
            valueDisplay.textContent = formatter ? formatter(newValue) : newValue;
            this.setSetting(key, newValue);
        });

        rangeContainer.append(range, valueDisplay);
        container.append(labelEl, desc, rangeContainer);
        return container;
    }

    createButton(textKey, descKey, onClick, type = 'primary') {
        const container = document.createElement('div');
        container.className = 'setting-item';

        const button = window.Lang.createElement('button', textKey, `setting-button ${type}`);
        button.addEventListener('click', onClick);

        const desc = window.Lang.createElement('p', descKey, 'setting-description');

        container.append(button, desc);
        return container;
    }

    // Data management methods
    exportSettings() {
        const data = {
            settings: this.settings,
            customIcons: JSON.parse(localStorage.getItem('bookmark-custom-icons') || '{}'),
            customOrder: JSON.parse(localStorage.getItem('bookmark-custom-order') || '{}'),
            exportDate: new Date().toISOString(),
            version: '1.0.0'
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `bookmark-settings-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showNotification(window.Lang.t('messages.settingsExported'), 'success');
    }

    importSettings() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);

                    if (data.settings) {
                        this.settings = { ...this.defaultSettings, ...data.settings };
                        this.saveSettings();
                    }

                    if (data.customIcons) {
                        localStorage.setItem('bookmark-custom-icons', JSON.stringify(data.customIcons));
                    }

                    if (data.customOrder) {
                        localStorage.setItem('bookmark-custom-order', JSON.stringify(data.customOrder));
                    }

                    this.showNotification(window.Lang.t('messages.settingsImported'), 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } catch (err) {
                    this.showNotification(window.Lang.t('messages.importFailed'), 'error');
                }
            };
            reader.readAsText(file);
        });
        input.click();
    }

    resetSettings() {
        if (confirm(window.Lang.t('messages.confirmReset'))) {
            this.settings = { ...this.defaultSettings };
            this.saveSettings();
            this.showNotification(window.Lang.t('messages.settingsReset'), 'success');
            setTimeout(() => {
                this.createSettingsPage();
            }, 500);
        }
    }

    clearAllData() {
        if (confirm(window.Lang.t('messages.confirmClearData'))) {
            localStorage.removeItem('bookmark-settings');
            localStorage.removeItem('bookmark-custom-icons');
            localStorage.removeItem('bookmark-custom-order');

            this.settings = { ...this.defaultSettings };
            this.showNotification(window.Lang.t('messages.dataCleared'), 'success');
            setTimeout(() => {
                location.reload();
            }, 1000);
        }
    }

    showNotification(message, type = 'info') {
        // Remove existing notification
        const existing = document.querySelector('.settings-notification');
        if (existing) existing.remove();

        const notification = document.createElement('div');
        notification.className = `settings-notification ${type}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    // Language switching method
    async switchLanguage(langCode) {
        try {
            // Show loading indicator
            const loadingDiv = document.createElement('div');
            loadingDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 18px;
                color: #666;
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
                z-index: 1000;
            `;
            loadingDiv.textContent = 'Loading...';

            // Clear current content and show loading
            Dom.TopContent.innerHTML = "";
            Dom.TopContent.appendChild(loadingDiv);

            // Load new language
            await window.Lang.setLanguage(langCode);

            // Recreate settings page with new language
            await this.createSettingsPage();

            // Show success notification
            this.showNotification(window.Lang.t('messages.settingsImported'), 'success');

        } catch (error) {
            console.error('Language switch failed:', error);
            // Fallback to recreating page with current language
            await this.createSettingsPage();
            this.showNotification('Language switch failed', 'error');
        }
    }

    // Legacy methods for compatibility
    Action() {
        this.AppendElement();
    }

    CreateWindow() {
        this.AppendElement();
    }
}