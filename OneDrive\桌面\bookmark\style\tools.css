/* Tool Page Styles - Apple Design */
#tool-container {
    width: 100%;
    height: 100%;
    padding: 40px 20px;
    box-sizing: border-box;
    background: #f5f5f7;
    overflow-y: auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.page-header {
    text-align: center;
    margin-bottom: 40px;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1d1d1f;
    margin: 0;
    letter-spacing: -0.02em;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.tool-item {
    background: #ffffff;
    border-radius: 20px;
    padding: 32px 24px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.04);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 200px;
    position: relative;
    overflow: hidden;
}

.tool-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
    border-color: rgba(0, 122, 255, 0.2);
}

.tool-icon {
    font-size: 2.5rem;
    margin-bottom: 20px;
    width: 72px;
    height: 72px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #007AFF;
    border-radius: 18px;
    color: white;
    box-shadow: 0 4px 20px rgba(0, 122, 255, 0.25);
}

.tool-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 8px;
    line-height: 1.4;
}

.tool-description {
    font-size: 0.9rem;
    color: #86868b;
    line-height: 1.5;
    flex-grow: 1;
    display: flex;
    align-items: center;
}

/* Apple-style icon colors */
.color-picker-icon {
    background: #FF3B30 !important;
}

.qr-icon {
    background: #5856D6 !important;
}

.json-icon {
    background: #007AFF !important;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
    font-weight: 600;
}

.base64-icon {
    background: #AF52DE !important;
}

.url-icon {
    background: #007AFF !important;
}

.password-icon {
    background: #34C759 !important;
}

.image-icon {
    background: #FF9500 !important;
}

.regex-icon {
    background: #FF2D92 !important;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
    font-weight: 600;
}

.markdown-icon {
    background: #8E8E93 !important;
}

.calc-icon {
    background: #007AFF !important;
}

.unit-icon {
    background: #5AC8FA !important;
}

.code-icon {
    background: #8E8E93 !important;
}

/* Dark theme support - Apple style */
.theme-dark #tool-container {
    background: #000000;
}

.theme-dark .page-title {
    color: #f2f2f7;
}

.theme-dark .tool-item {
    background: #1c1c1e;
    border: 1px solid rgba(255, 255, 255, 0.06);
}

.theme-dark .tool-item:hover {
    background: #2c2c2e;
    border-color: rgba(0, 122, 255, 0.3);
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.3);
}

.theme-dark .tool-name {
    color: #f2f2f7;
}

.theme-dark .tool-description {
    color: #8e8e93;
}

/* Responsive design */
@media (max-width: 768px) {
    #tool-container {
        padding: 16px;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .tools-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 16px;
    }
    
    .tool-item {
        padding: 20px;
        min-height: 160px;
    }
    
    .tool-icon {
        font-size: 2.5rem;
        width: 70px;
        height: 70px;
        margin-bottom: 12px;
    }
    
    .tool-name {
        font-size: 1.1rem;
    }
    
    .tool-description {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .tools-grid {
        grid-template-columns: 1fr;
    }
    
    .tool-item {
        padding: 16px;
        min-height: 140px;
    }
    
    .tool-icon {
        font-size: 2rem;
        width: 60px;
        height: 60px;
    }
}

/* Animation for page load */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tool-item {
    animation: fadeInUp 0.6s ease forwards;
}

.tool-item:nth-child(1) { animation-delay: 0.1s; }
.tool-item:nth-child(2) { animation-delay: 0.2s; }
.tool-item:nth-child(3) { animation-delay: 0.3s; }
.tool-item:nth-child(4) { animation-delay: 0.4s; }
.tool-item:nth-child(5) { animation-delay: 0.5s; }
.tool-item:nth-child(6) { animation-delay: 0.6s; }
.tool-item:nth-child(7) { animation-delay: 0.7s; }
.tool-item:nth-child(8) { animation-delay: 0.8s; }
.tool-item:nth-child(9) { animation-delay: 0.9s; }
.tool-item:nth-child(10) { animation-delay: 1.0s; }
.tool-item:nth-child(11) { animation-delay: 1.1s; }
.tool-item:nth-child(12) { animation-delay: 1.2s; }

/* Bookmark specific styles - Apple design */
.bookmark-item .tool-icon {
    background: #007AFF;
    overflow: hidden;
    border-radius: 18px;
}

.bookmark-item .tool-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 18px;
}

.bookmark-item .tool-icon.fallback-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    background: #8E8E93;
}

.tool-url {
    font-size: 0.8rem;
    color: #8e8e93;
    margin-top: 6px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
    font-weight: 500;
}

.theme-dark .tool-url {
    color: #636366;
}

/* Loading state - Apple style */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    text-align: center;
    color: #1d1d1f;
}

.theme-dark .loading-state {
    color: #f2f2f7;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(142, 142, 147, 0.3);
    border-top: 3px solid #007AFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 24px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Message states - Apple style */
.bookmark-access-message,
.error-message,
.no-tools-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 40px;
    text-align: center;
    color: #1d1d1f;
    max-width: 600px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.theme-dark .bookmark-access-message,
.theme-dark .error-message,
.theme-dark .no-tools-message {
    background: #1c1c1e;
    color: #f2f2f7;
}

.access-icon,
.error-icon,
.no-tools-icon {
    font-size: 4rem;
    margin-bottom: 24px;
    opacity: 0.8;
}

.bookmark-access-message h3,
.error-message h3,
.no-tools-message h3 {
    font-size: 1.5rem;
    margin: 0 0 16px 0;
    font-weight: 600;
    letter-spacing: -0.01em;
}

.bookmark-access-message p,
.error-message p,
.no-tools-message p {
    font-size: 1rem;
    line-height: 1.6;
    margin: 0 0 24px 0;
    color: #86868b;
}

.theme-dark .bookmark-access-message p,
.theme-dark .error-message p,
.theme-dark .no-tools-message p {
    color: #8e8e93;
}

.fallback-tools {
    margin-top: 32px;
}

.fallback-tool {
    display: inline-block;
    background: #007AFF;
    color: white;
    text-decoration: none;
    padding: 12px 20px;
    margin: 6px;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fallback-tool:hover {
    background: #0056CC;
    text-decoration: none;
    color: white;
    transform: translateY(-1px);
}

/* No animations mode */
.no-animations .tool-item {
    animation: none !important;
}

.no-animations .tool-item:hover {
    transform: none !important;
}

.no-animations .loading-spinner {
    animation: none !important;
}
