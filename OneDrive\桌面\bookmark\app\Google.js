class Google {

    AppendElement() {
        this.CreateElement()
    }

    // Refresh the Google page content (useful when language changes)
    refresh() {
        const googleContainer = document.getElementById('googlecontainer');
        if (googleContainer) {
            googleContainer.remove();
        }
        this.CreateElement();
    }

    CreateElement() {
        const googlecontainer = document.createElement('div');
        googlecontainer.id = "googlecontainer";

        
        const googlecard = document.createElement('div');
        googlecard.id = "googlecard";

        
        const googletit = document.createElement('div');
        googletit.id = "googletit";
        googletit.textContent = this.getTitle();
        googlecard.appendChild(googletit);

        
        const appcontainer = document.createElement('div');
        appcontainer.id = "appcontainer";

     
        const apps = this.getApps();

        apps.forEach(app => {
            const appitem = document.createElement('div');
            appitem.className = "appitem";
            appitem.onclick = () => window.location.href = app.link;

            const appicon = document.createElement('div');
            appicon.className = "appicon";
            appicon.id = app.iconId;

            const appname = document.createElement('div');
            appname.className = "appname";
            appname.textContent = app.name;

            appitem.appendChild(appicon);
            appitem.appendChild(appname);
            appcontainer.appendChild(appitem);
        });

        googlecard.appendChild(appcontainer);
        googlecontainer.appendChild(googlecard);
        Dom.TopContent.appendChild(googlecontainer);
    }

    // Get title based on current language
    getTitle() {
        if (window.Lang) {
            return window.Lang.t('google.title');
        }
        return 'Google 應用';
    }

    // Get apps list with localized names
    getApps() {
        const appConfigs = [
            { iconId: "gmailicon", nameKey: "gmail", link: "https://mail.google.com/mail/u/0/#inbox" },
            { iconId: "yticon", nameKey: "youtube", link: "https://www.youtube.com/" },
            { iconId: "mapicon", nameKey: "maps", link: "https://www.google.com/maps?authuser=0" },
            { iconId: "driveicon", nameKey: "drive", link: "https://drive.google.com/drive/home" },
            { iconId: "meeticon", nameKey: "meet", link: "https://meet.google.com/landing?hs=197&authuser=0&pli=1" },
            { iconId: "dateicon", nameKey: "calendar", link: "https://calendar.google.com/calendar" },
            { iconId: "chromeadditionicon", nameKey: "chromeStore", link: "https://chromewebstore.google.com/" },
            { iconId: "geminiicon", nameKey: "gemini", link: "https://gemini.google.com/app" },
            { iconId: "googleplayicon", nameKey: "play", link: "https://play.google.com/store/games?device=windows&pli=1" },
            { iconId: "googleformsicon", nameKey: "forms", link: "https://docs.google.com/forms/u/0/" },
            { iconId: "googletranslationicon", nameKey: "translation", link: "https://translate.google.com/" }
        ];

        return appConfigs.map(app => ({
            iconId: app.iconId,
            name: window.Lang ? window.Lang.t(`google.apps.${app.nameKey}`) : app.nameKey,
            link: app.link
        }));
    }
}